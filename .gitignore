# Dependencies
node_modules/
venv/
__pycache__/

# Environment variables
.env
.env.local
.env.*
*.env

# Build outputs
dist/
build/
*.pyc
.vite/

# Maven specific
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

# IDE specific files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*.iml
*.ipr
*.iws
.project
.classpath
.settings/
.factorypath
.metadata/
.recommenders/
nbproject/
nbactions.xml
nb-configuration.xml
.nb-gradle/
.eclipse/

# Operating System Files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
hs_err_pid*

# Testing
coverage/
.pytest_cache/

# Database
*.sqlite3
mysql-data/

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*.cache
.eslintcache
.stylelintcache
.sass-cache/
.cache/
.parcel-cache/

# Python virtual environments
.venv/

# Sensitive files
**/application-production.properties
**/application-local.properties
**/application-dev.properties
**/secrets.properties
**/credentials.json
**/key.json
**/service-account*.json
*secrets*/
*credential*/

# Deployment scripts with potential secrets
scripts/deploy-*.ps1
scripts/deploy-*.sh
scripts/*-cloudrun*.ps1
scripts/*-cloudrun*.sh

# Large files
*.psd
*.pdf
*.zip
*.rar
*.7z
*.tar
*.gz
*.mp4
*.mov
*.avi
*.wmv
*.iso
*.dmg
*.jar
*.war
*.ear

# Frontend specific
src/Frontend/.pnpm-store/
src/Frontend/.pnpm-debug.log

# Specific large files detected by GitHub
510c168ab8e9f7f19c193bc73bcae52290664a2d
29c917202f5db259a88d2bbca1d9714d598ecfef
