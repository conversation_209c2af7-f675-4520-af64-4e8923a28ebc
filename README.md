# AI-Powered ERP Document Verification System

This project implements an advanced AI-powered document verification system for ERP job documents. It uses Google Gemini 2.0 AI models to intelligently extract and verify data between Microsoft Dynamics 365 Business Central and associated PDF documents (Job Consumption, Proforma Invoice, Sales Quote). The system features a modern web interface for monitoring and managing verifications, with comprehensive backend services handling the verification logic.

## Features

*   **Business Central Integration:** Fetches Job Ledger data via BC OData API.
*   **Advanced AI Document Processing:**
    *   **Expert OCR System:** Uses Google Gemini 2.0 models as an intelligent OCR system that understands document structure and context.
    *   **Intelligent Data Extraction:** Extracts key identifiers and data from documents (Sales Quote Number, Tax Invoice Number, Job Number, line items, totals).
    *   **Comprehensive Verification Engine:** Java-based verification logic performs detailed cross-document and Business Central validation.
    *   **Document Classification:** AI automatically classifies documents by type (Sales Quote, Proforma Invoice, Job Consumption).
    *   **Multi-level Validation:** Verifies header fields, line items, totals, and business rules.
    *   **Signature Detection:** Checks for presence of required signatures in Job Consumption documents.
*   **Modern Microservice Architecture:**
    *   **Java Spring Boot Backend:** Handles business logic, data persistence, and orchestration.
    *   **Python FastAPI AI Service:** Dedicated microservice for Gemini AI integration and document processing.
    *   **SharePoint Integration:** Retrieves documents directly from SharePoint via Business Central links.
    *   **Reactive Programming:** Uses Spring WebFlux for non-blocking, scalable operations.
*   **Robust Processing Pipeline:**
    *   **Asynchronous Processing:** Background verification using Spring's @Async with proper transaction management.
    *   **Error Handling:** Comprehensive system error handling with retry logic and user-friendly error messages.
    *   **Fail-Fast Validation:** Stops processing immediately when critical validation failures occur.
*   **Modern Web Interface (React/TypeScript):**
    *   **Interactive Dashboard:** Real-time statistics, charts, and activity monitoring.
    *   **Job Management:** Comprehensive job listing with filtering and search capabilities.
    *   **Detailed Job Views:** Complete verification history and discrepancy analysis.
    *   **Batch Verification:** Manager interface for checking eligibility and processing multiple jobs.
    *   **Document Viewer:** View and download job documents directly from the interface.
    *   **Real-time Updates:** Live status updates and progress tracking.
    *   **Role-based Access:** Granular permissions for different user types.
    *   **Modern UI/UX:** Clean, responsive design with light/dark mode support.
*   **Enterprise Data Management:**
    *   **MySQL Database:** Robust data persistence with Spring Data JPA and Hibernate.
    *   **Comprehensive Logging:** Detailed activity logs for audit trails and monitoring.
    *   **Data Integrity:** Transaction management and consistency guarantees.
    *   **Performance Optimization:** Efficient queries and caching strategies.

## Technology Stack

### Frontend
-   **Framework/Language:** React with TypeScript
-   **Build Tool:** Vite
-   **Styling:** Tailwind CSS
-   **Component Library:** shadcn/ui
-   **Routing:** React Router (`react-router-dom`)
-   **API Client:** Axios
-   **Charting:** Recharts
-   **Date Formatting:** `date-fns`
-   **Notifications:** `sonner`
-   **Package Manager:** pnpm

### Java Backend
-   **Framework/Language:** Java 17+ with Spring Boot 3.2+
-   **Build Tool:** Maven
-   **Database:** MySQL 8.0+ with Spring Data JPA / Hibernate
-   **Web Framework:** Spring Web MVC (REST Controllers) + Spring WebFlux (Reactive)
-   **External Integrations:**
    -   Spring WebFlux (`WebClient`) for Business Central OData API
    -   SharePoint document retrieval with Azure AD authentication
-   **Document Processing:** Apache PDFBox for PDF to image conversion
-   **Asynchronous Processing:** Spring's @Async with proper transaction management
-   **Security:** Spring Security with JWT authentication
-   **JSON Handling:** Jackson with custom serialization
-   **Logging:** SLF4j / Logback with structured logging
-   **Utilities:** Lombok, MapStruct for mapping
-   **Testing:** JUnit 5, Mockito, Spring Boot Test

### Python AI Service
-   **Framework/Language:** Python 3.10+ with FastAPI
-   **AI Models:** Google Gemini 2.0 (gemini-2.0-flash-001, gemini-2.0-flash-lite-001)
-   **Cloud Integration:** Google Cloud Vertex AI SDK with service account authentication
-   **Image Processing:** Pillow (PIL) for image manipulation and optimization
-   **Data Validation:** Pydantic for request/response schemas and validation
-   **Logging:** Structured logging with Python's logging module
-   **Environment Management:** Python-dotenv for configuration
-   **HTTP Client:** httpx for async HTTP requests
-   **Development:** uvicorn ASGI server with hot reload

### Infrastructure & DevOps
-   **Database:** Google Cloud SQL (MySQL 8.0+) with automated backups and high availability
-   **Cloud Services:** Google Cloud Platform with Vertex AI API
-   **Containerization:** Docker with multi-stage builds for all services
-   **Orchestration:** Google Cloud Run for serverless container deployment
-   **Frontend Hosting:** Google Cloud Storage with Cloud CDN for global distribution
-   **Monitoring:** Cloud Logging, Cloud Monitoring, and comprehensive health checks
-   **Security:** Google Secret Manager for credentials and environment-based configuration

## Recent System Improvements

### Code Quality & Performance Optimizations (Latest Update)
-   **✅ Complete Codebase Cleanup:** Removed all unused imports, variables, and methods across Java and Python services
-   **✅ Enhanced Error Handling:** Improved system error handling with user-friendly messages and proper retry logic
-   **✅ Optimized Dependencies:** Cleaned up Maven and npm dependencies, removed redundant packages
-   **✅ Code Standards:** Added missing annotations, fixed code style issues, improved documentation
-   **✅ Performance Improvements:** Optimized database queries and reduced memory footprint
-   **✅ Security Enhancements:** Updated authentication mechanisms and secured API endpoints

### Architecture Improvements
-   **✅ Enhanced Verification Engine:** Redesigned verification logic for better accuracy and performance
-   **✅ Improved Document Processing:** Streamlined document classification and data extraction
-   **✅ Better Transaction Management:** Implemented proper transaction boundaries and error recovery
-   **✅ Optimized SharePoint Integration:** Enhanced document retrieval with better error handling
-   **✅ Modernized UI Components:** Updated frontend components for better user experience

## System Workflow (Manual Trigger & Verification Page)

```mermaid
graph TD
    subgraph Single Job Verification (Job Detail Page)
        A1[User clicks "Verify Job" on Job Detail] --> B1[Frontend POST /api/verifications];
        B1 --> C1[Backend creates VerificationRequest];
        C1 --> D1[Backend processes verification asynchronously];
        D1 --> E1[Backend performs verification (BC fetch, document processing)];
        E1 --> F1[Backend calls Python AI Service for document verification];
        F1 --> G1[Backend updates VerificationRequest & Job status];
    end

    subgraph Batch Verification (Job Verification Page)
        A2[User enters Job Number] --> B2{Backend GET /api/verifications/check-eligibility/{jobNo}};
        B2 -- Eligible --> C2[Display 'Add to Verification List' button];
        B2 -- Not Eligible --> D2[Display 'Not Eligible' message];
        C2 --> E2{User clicks 'Add to List'};
        E2 --> F2[Job added to Verification List (Frontend State)];
        G2[User views Verification List] --> H2{User clicks 'Verify All'};
        H2 --> I2[Frontend iterates list, POST /api/verifications for each];
        I2 --> C1;  // Re-uses the single verification backend flow
        D2 --> J2[End];
        F2 --> J2;
    end

    subgraph AI Document Processing
        F1 --> AI1[Download documents from SharePoint];
        AI1 --> AI2[Classify documents using Gemini AI];
        AI2 --> AI3[Extract all document data with Gemini OCR];
        AI3 --> AI4[Java Verification Engine validates data];
        AI4 --> AI5[Cross-document and BC validation];
        AI5 --> AI6[Update Business Central with results];
        AI6 --> G1;
    end

    subgraph Monitoring
        M1[Frontend fetches latest status via GET /api/verifications/job/{jobNo}/latest]
        M2[Frontend fetches dashboard data via GET /api/dashboard/*]
    end
```

**Detailed Flow (Single Trigger):**

1.  **Frontend:** User clicks "Verify Job" on a job detail page.
2.  **Frontend -> Backend:** `POST /api/verifications` request sent with `jobNo`.
3.  **Backend (`VerificationController`):**
    *   Finds/Creates `Job` entity in DB (status `PENDING`).
    *   Creates `VerificationRequest` entity (status `PENDING`, generates UUID). Saves both.
    *   *After DB commit*, triggers asynchronous processing via `VerificationProcessingService`.
    *   Returns `202 Accepted` with `verificationRequestId` to frontend.
4.  **Backend (`DocumentProcessingOrchestratorService`):**
    *   Processes the verification request asynchronously using Spring's `@Async` with proper transaction management.
    *   Finds `VerificationRequest` and `Job`. Updates statuses to `PROCESSING`.
    *   **Document Retrieval:** Fetches documents from SharePoint via Business Central JobAttachmentLinks API.
    *   **Document Classification:** Uses Gemini AI to classify documents by type (Sales Quote, Proforma Invoice, Job Consumption).
    *   **Data Extraction:** Uses Gemini AI as expert OCR to extract all document data in structured JSON format.
    *   **Verification Engine:** Java-based comprehensive verification performs:
        *   Cross-document validation (matching identifiers, totals)
        *   Business Central data validation (fetching BC data using extracted identifiers)
        *   Business rule validation (required fields, signatures, etc.)
    *   **Business Central Updates:** Updates verification fields with results and timestamps.
    *   Updates `VerificationRequest` status (`COMPLETED`/`FAILED`), timestamp, and detailed discrepancy analysis.
    *   Updates `Job` status (`VERIFIED`/`FLAGGED`/`ERROR`).
    *   Saves entities and logs comprehensive activity via `ActivityLogService`.
5.  **Frontend (Monitoring):**
    *   `JobDetail` page fetches latest results via `GET /api/verifications/job/{jobNo}/latest`.
    *   Dashboard components fetch data via `/api/dashboard/*` and `/api/activity-log`.

**Detailed Flow (Verification Page):**

1.  **Frontend:** User navigates to the "Job Verification" page.
2.  **Frontend:** User enters a `jobNo` and clicks "Check Eligibility".
3.  **Frontend -> Backend:** `GET /api/verifications/check-eligibility/{jobNo}` request sent.
4.  **Backend (`VerificationController`):**
    *   Calls `BusinessCentralService` to fetch the job entry.
    *   Checks if `firstCheckDate` is present and `secondCheckDate` is absent.
    *   Returns `200 OK` with `{ "isEligible": boolean, "message": "...", ... }`.
5.  **Frontend:** Displays eligibility status and message. If eligible, shows "Add to Verification List" button.
6.  **Frontend:** User clicks "Add to Verification List". Job details are added to a local state array.
7.  **Frontend:** User clicks "Verify All" on the list.
8.  **Frontend:** Iterates through the local list, sending a `POST /api/verifications` request for each `jobNo`.
9.  **Backend:** Each `POST` request follows the "Single Trigger" flow from step 3 onwards.

## Getting Started

### Prerequisites

Make sure you have the following installed on your system:

-   **Node.js:** LTS version recommended (e.g., 18+). Download from [nodejs.org](https://nodejs.org/).
-   **pnpm:** A fast, disk space efficient package manager. Install via Node.js: `npm install -g pnpm`.
-   **Java JDK:** Version 17 or later. Download from [Oracle](https://www.oracle.com/java/technologies/downloads/), [OpenJDK](https://openjdk.java.net/), or use a package manager.
-   **Maven:** Version 3.8+ (often bundled with IDEs, or download from [maven.apache.org](https://maven.apache.org/download.cgi)).
-   **Python:** Version 3.10+. Download from [python.org](https://www.python.org/downloads/).
-   **MySQL Server:** Version 8.0+. Download from [mysql.com](https://dev.mysql.com/downloads/mysql/) or use Docker/package manager.
-   **Git:** Download from [git-scm.com](https://git-scm.com/downloads).
-   **Google Cloud Account:** With Vertex AI API enabled and appropriate credentials.

### Installation & Setup

1.  **Clone the Repository:**
    ```bash
    git clone https://github.com/emagero0/AI-powered-ERP-solution.git # Replace with your repo URL if different
    cd AI-powered-ERP-solution
    ```

2.  **Configure Java Backend Services:**
    *   **Database (MySQL):**
        *   Ensure your MySQL server is running.
        *   Create a database for the application (e.g., `aierpdb`).
        *   Open `src/backend/src/main/resources/application.properties`.
        *   Update `spring.datasource.url` (if your DB name or host/port differs).
        *   Update `spring.datasource.username` and `spring.datasource.password` with your MySQL credentials.
        *   *Note: `spring.jpa.hibernate.ddl-auto=update` will attempt to create/update tables automatically on startup.*
    *   **Business Central API:**
        *   Update the `dynamics.bc.odata.base-url`, `dynamics.bc.odata.username`, and `dynamics.bc.odata.key` properties in `application.properties` with your BC environment details.
    *   **LLM Service Configuration:**
        *   Update the `llm.python.service.baseurl` property in `application.properties` to point to your Python service (default: `http://localhost:8001` for local development).

3.  **Configure Python AI Service:**
    *   Navigate to the Python service directory:
        ```bash
        cd src/gemini-python-service
        ```
    *   Create a virtual environment:
        ```bash
        python -m venv venv
        ```
    *   Activate the virtual environment:
        ```bash
        # On Windows
        venv\Scripts\activate
        # On macOS/Linux
        source venv/bin/activate
        ```
    *   Install dependencies:
        ```bash
        pip install -r requirements.txt
        ```
    *   Create a `.env` file in the `src/gemini-python-service` directory with your Google Cloud credentials:
        ```
        GCP_PROJECT_ID=your-project-id
        GCP_LOCATION=us-central1
        GEMINI_MODEL_NAME=gemini-2.0-flash-001
        # Optional: Path to service account key file if not using application default credentials
        # GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
        ```

4.  **Prepare Test Data (Optional):**
    *   Place the necessary PDF documents for testing (e.g., for job J069023) in the correct directory structure: `src/Jobs 2nd check pdf/J069023/Job Consumption.pdf`, `src/Jobs 2nd check pdf/J069023/ProformaInvoice.pdf`, etc.

5.  **Start the Python AI Service:**
    *   With the virtual environment activated, run:
        ```bash
        python -m app.main
        ```
    *   The Python service should now be running at `http://localhost:8001`.

6.  **Build and Run the Java Backend:**
    *   Open a **new terminal** and navigate to the backend directory:
        ```bash
        cd src/backend
        ```
    *   Build the project using Maven (this also downloads dependencies):
        ```bash
        mvn clean install -DskipTests # Use -DskipTests if you want to skip running tests during build
        ```
    *   Run the application:
        ```bash
        mvn spring-boot:run
        ```
    *   The backend API should now be running, typically at `http://localhost:8081`. Check the console output for the exact port and status.

7.  **Install and Run the Frontend (React/Vite):**
    *   Open a **new terminal** and navigate to the frontend directory:
        ```bash
        cd src/frontend
        ```
    *   Install dependencies using pnpm:
        ```bash
        pnpm install
        ```
    *   Start the development server:
        ```bash
        pnpm run dev
        ```
    *   The frontend application should now be running, typically at `http://localhost:5173`. Open this URL in your web browser.

8.  **Cloud Deployment (Google Cloud Run):**
    *   The project is configured for serverless deployment on Google Cloud Platform.
    *   Ensure your `.env` file contains all required credentials:
        ```bash
        # Database credentials for Cloud SQL
        DB_USERNAME=your-db-username
        DB_PASSWORD=your-db-password

        # Google Cloud configuration
        GCP_PROJECT_ID=your-project-id
        GCP_LOCATION=us-central1

        # Service account credentials (inline format)
        GOOGLE_SERVICE_ACCOUNT_*=your-service-account-details
        ```
    *   Deploy using the provided Cloud Run scripts:
        ```bash
        # Deploy backend service
        ./scripts/deploy-backend-cloudrun.ps1

        # Or use the migration script for full deployment
        ./scripts/migrate-to-cloud-run.ps1 all
        ```

9.  **CI/CD Setup:**
    *   The project is configured for CI/CD pipelines.
    *   See [CI_CD_SETUP.md](CI_CD_SETUP.md) for detailed instructions on setting up CI/CD.
    *   See [ENVIRONMENT_SETUP.md](ENVIRONMENT_SETUP.md) for information on required environment variables.

## API Endpoints

*   `POST /api/verifications`: Trigger a new verification request.
    *   Body: `{ "jobNo": "string" }`
    *   Response: `202 Accepted`, Body: `{ "verificationRequestId": "string" }`
*   `GET /api/verifications/check-eligibility/{jobNo}`: Check if a job is eligible for verification (1st check done, 2nd check pending).
    *   Response: `200 OK`, Body: `EligibilityCheckResponseDTO`
*   `GET /api/verifications/{id}`: Get details of a specific verification request by its UUID.
*   `GET /api/verifications/job/{jobNo}/latest`: Get the latest verification request details for a specific Job Number.
*   `GET /api/jobs`: Get a summary list of all jobs.
*   `GET /api/jobs/{id}`: Get detailed information for a job by its internal database ID.
*   `GET /api/dashboard/stats`: Get overall dashboard statistics.
*   `GET /api/dashboard/daily-stats`: Get aggregated daily verification counts for the last 7 days.
*   `GET /api/activity-log`: Get paginated recent activity logs.
*   `POST /api/feedback`: Submit user feedback for a job.

## Project Status

### Current Version: v2.0 (Production Ready)
- ✅ **Fully Functional:** All core features implemented and tested
- ✅ **Production Deployed:** Successfully running in production environment
- ✅ **Code Quality:** Clean, optimized, and well-documented codebase
- ✅ **Performance Optimized:** Efficient processing with proper error handling
- ✅ **Security Hardened:** Secure authentication and data protection

### Maintenance & Support
- **Active Development:** Regular updates and feature enhancements
- **Bug Fixes:** Prompt resolution of issues and improvements
- **Documentation:** Comprehensive documentation and setup guides
- **Monitoring:** Continuous monitoring and performance optimization

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ using cutting-edge AI technology and modern software engineering practices.**
