# 🚀 **Serverless Deployment Guide - Google Cloud**

## **📋 Complete Serverless Deployment Plan for AI-Powered ERP System**

This guide provides step-by-step instructions for deploying your AI-powered ERP document verification system on Google Cloud Platform using serverless containers and managed services.

---

## **🏗️ Serverless Architecture Overview**

### **Infrastructure Components:**
- **Cloud Run**: Serverless container platform for backend and AI services
- **Cloud SQL**: Fully managed MySQL database with automated backups
- **Cloud Storage + CDN**: Static website hosting for React frontend
- **Vertex AI**: Gemini 2.0 AI models for document processing
- **Secret Manager**: Secure credential storage
- **Cloud Monitoring**: Comprehensive logging and monitoring

### **Benefits of Serverless Architecture:**
- ✅ **Cost Optimization**: Pay only for actual usage, automatic scaling to zero
- ✅ **High Availability**: Built-in redundancy and automatic failover
- ✅ **Simplified Management**: No infrastructure maintenance required
- ✅ **Auto Scaling**: Automatic scaling based on demand
- ✅ **Global Distribution**: CDN for fast frontend delivery worldwide

---

## **🛠️ Prerequisites**

### **Required Tools:**
```bash
# Install Google Cloud SDK
# Download from: https://cloud.google.com/sdk/docs/install

# Verify installation
gcloud version
```

### **Required Permissions:**
- Cloud Run Admin
- Cloud SQL Admin
- Storage Admin
- Secret Manager Admin
- Vertex AI User
- Service Account Admin

---

## **📦 Step-by-Step Deployment**

### **Step 1: Environment Setup**

1. **Configure Google Cloud Project**
```bash
# Set your project ID
export PROJECT_ID="your-project-id"
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable run.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable aiplatform.googleapis.com
```

2. **Configure Authentication**
```bash
# Authenticate with Google Cloud
gcloud auth login
gcloud auth application-default login
```

### **Step 2: Database Setup (Cloud SQL)**

1. **Create Cloud SQL Instance**
```bash
# Create MySQL instance
gcloud sql instances create erp-database \
    --database-version=MYSQL_8_0 \
    --tier=db-f1-micro \
    --region=us-central1 \
    --storage-type=SSD \
    --storage-size=20GB \
    --backup-start-time=03:00 \
    --enable-bin-log \
    --maintenance-window-day=SUN \
    --maintenance-window-hour=04

# Create database
gcloud sql databases create aierpdb --instance=erp-database

# Create database user
gcloud sql users create erp-user \
    --instance=erp-database \
    --password=your-secure-password
```

2. **Configure Database Connection**
```bash
# Get connection name for Cloud Run
gcloud sql instances describe erp-database --format="value(connectionName)"
```

### **Step 3: Secrets Management**

1. **Store Database Credentials**
```bash
# Store database password
echo -n "your-secure-password" | gcloud secrets create db-password --data-file=-

# Store Business Central credentials
echo -n "your-bc-api-key" | gcloud secrets create bc-api-key --data-file=-
```

2. **Store Service Account Credentials**
```bash
# Create service account for Vertex AI
gcloud iam service-accounts create vertex-ai-service \
    --display-name="Vertex AI Service Account"

# Grant necessary permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:vertex-ai-service@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

# Create and store service account key
gcloud iam service-accounts keys create key.json \
    --iam-account=vertex-ai-service@$PROJECT_ID.iam.gserviceaccount.com

# Store in Secret Manager
gcloud secrets create vertex-ai-key --data-file=key.json
```

### **Step 4: Build and Deploy Services**

1. **Build Docker Images**
```bash
# Build backend image
cd src/backend
gcloud builds submit --tag gcr.io/$PROJECT_ID/erp-backend

# Build AI service image
cd ../gemini-python-service
gcloud builds submit --tag gcr.io/$PROJECT_ID/erp-ai-service

# Build frontend (for Cloud Storage deployment)
cd ../Frontend
npm install
npm run build
```

2. **Deploy Backend Service**
```bash
# Deploy backend to Cloud Run
gcloud run deploy erp-backend \
    --image=gcr.io/$PROJECT_ID/erp-backend \
    --platform=managed \
    --region=us-central1 \
    --allow-unauthenticated \
    --port=8081 \
    --memory=2Gi \
    --cpu=2 \
    --min-instances=0 \
    --max-instances=10 \
    --timeout=900 \
    --add-cloudsql-instances=$PROJECT_ID:us-central1:erp-database \
    --set-env-vars="SPRING_PROFILES_ACTIVE=production" \
    --set-env-vars="SPRING_DATASOURCE_URL=**********************************************************************************************************************************" \
    --set-env-vars="DB_USERNAME=erp-user" \
    --set-secrets="DB_PASSWORD=db-password:latest" \
    --set-secrets="DYNAMICS_BC_API_KEY=bc-api-key:latest"
```

3. **Deploy AI Service**
```bash
# Deploy AI service to Cloud Run
gcloud run deploy erp-ai-service \
    --image=gcr.io/$PROJECT_ID/erp-ai-service \
    --platform=managed \
    --region=us-central1 \
    --allow-unauthenticated \
    --port=8000 \
    --memory=2Gi \
    --cpu=2 \
    --min-instances=0 \
    --max-instances=5 \
    --timeout=600 \
    --set-env-vars="GCP_PROJECT_ID=$PROJECT_ID" \
    --set-env-vars="GCP_LOCATION=us-central1" \
    --set-secrets="GOOGLE_APPLICATION_CREDENTIALS=vertex-ai-key:latest"
```

### **Step 5: Frontend Deployment**

1. **Create Storage Bucket**
```bash
# Create bucket for frontend hosting
gsutil mb gs://$PROJECT_ID-erp-frontend

# Enable static website hosting
gsutil web set -m index.html -e index.html gs://$PROJECT_ID-erp-frontend

# Make bucket publicly readable
gsutil iam ch allUsers:objectViewer gs://$PROJECT_ID-erp-frontend
```

2. **Deploy Frontend**
```bash
# Upload built frontend files
cd src/Frontend/dist
gsutil -m rsync -r -d . gs://$PROJECT_ID-erp-frontend

# Set up Cloud CDN (optional but recommended)
gcloud compute backend-buckets create erp-frontend-bucket \
    --gcs-bucket-name=$PROJECT_ID-erp-frontend
```

### **Step 6: Configuration Updates**

1. **Update Backend Service URL in Frontend**
```bash
# Get backend service URL
BACKEND_URL=$(gcloud run services describe erp-backend --region=us-central1 --format="value(status.url)")

# Update frontend configuration and redeploy
# Edit your frontend environment configuration with the backend URL
```

2. **Update AI Service URL in Backend**
```bash
# Get AI service URL
AI_SERVICE_URL=$(gcloud run services describe erp-ai-service --region=us-central1 --format="value(status.url)")

# Update backend service with AI service URL
gcloud run services update erp-backend \
    --region=us-central1 \
    --set-env-vars="LLM_PYTHON_SERVICE_BASEURL=$AI_SERVICE_URL"
```

---

## **📊 Monitoring and Maintenance**

### **View Logs**
```bash
# Backend logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=erp-backend" --limit=50

# AI service logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=erp-ai-service" --limit=50
```

### **Monitor Performance**
```bash
# View service metrics
gcloud run services describe erp-backend --region=us-central1
gcloud run services describe erp-ai-service --region=us-central1
```

### **Database Management**
```bash
# Connect to Cloud SQL
gcloud sql connect erp-database --user=erp-user

# Create database backup
gcloud sql backups create --instance=erp-database
```

---

## **💰 Cost Optimization**

### **Recommended Settings:**
- **Cloud Run**: Use minimum instances = 0 for cost savings
- **Cloud SQL**: Use db-f1-micro for development, scale up for production
- **Storage**: Enable lifecycle policies for old logs and backups

### **Monitoring Costs:**
```bash
# Set up billing alerts
gcloud alpha billing budgets create \
    --billing-account=YOUR_BILLING_ACCOUNT_ID \
    --display-name="ERP System Budget" \
    --budget-amount=100USD
```

---

## **🔒 Security Best Practices**

1. **Use IAM roles with least privilege**
2. **Enable VPC connector for private communication (optional)**
3. **Use Secret Manager for all sensitive data**
4. **Enable audit logging**
5. **Set up monitoring and alerting**

---

## **🚀 Next Steps**

1. **Set up custom domain** for frontend
2. **Configure SSL certificates**
3. **Set up CI/CD pipeline** for automated deployments
4. **Implement monitoring dashboards**
5. **Set up automated backups and disaster recovery**

---

**🎉 Your AI-Powered ERP system is now running on Google Cloud serverless infrastructure!**
