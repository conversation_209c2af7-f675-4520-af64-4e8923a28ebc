package com.erp.aierpbackend;

import io.github.cdimascio.dotenv.Dotenv;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

import java.io.File;

@SpringBootApplication
@EnableAsync // Enable asynchronous method execution
public class AiErpBackendApplication {

    public static void main(String[] args) {
        // Load .env file before Spring Boot starts
        loadDotenvEarly();

        SpringApplication.run(AiErpBackendApplication.class, args);
    }

    /**
     * Load .env file early in the application startup process
     * This ensures environment variables are available before Spring Boot configuration
     */
    private static void loadDotenvEarly() {
        try {
            File backendEnvFile = new File("src/backend/.env");
            File rootEnvFile = new File(".env");

            Dotenv dotenv = null;

            if (backendEnvFile.exists()) {
                dotenv = Dotenv.configure()
                    .directory("src/backend")
                    .filename(".env")
                    .ignoreIfMissing()
                    .load();
                System.out.println("🚀 Early loaded .env from: src/backend/.env");
            } else if (rootEnvFile.exists()) {
                dotenv = Dotenv.configure()
                    .directory(".")
                    .filename(".env")
                    .ignoreIfMissing()
                    .load();
                System.out.println("🚀 Early loaded .env from: ./.env");
            }

            // Set system properties for Spring Boot to pick up
            if (dotenv != null) {
                dotenv.entries().forEach(entry -> {
                    String key = entry.getKey();
                    String value = entry.getValue();

                    // Set as system property for Spring Boot
                    if (System.getProperty(key) == null) {
                        System.setProperty(key, value);
                    }
                });
            }

        } catch (Exception e) {
            System.err.println("⚠️  Warning: Could not load .env file: " + e.getMessage());
        }
    }

}
