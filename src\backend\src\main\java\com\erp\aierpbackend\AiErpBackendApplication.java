package com.erp.aierpbackend;

import io.github.cdimascio.dotenv.Dotenv;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

import java.io.File;

@SpringBootApplication
@EnableAsync // Enable asynchronous method execution
public class AiErpBackendApplication {

    public static void main(String[] args) {
        // Load .env file before Spring Boot starts
        loadDotenvEarly();

        SpringApplication.run(AiErpBackendApplication.class, args);
    }

    /**
     * Load .env file early in the application startup process
     * This ensures environment variables are available before Spring Boot configuration
     * Prioritizes root .env file over backend-specific .env file
     */
    private static void loadDotenvEarly() {
        try {
            File rootEnvFile = new File(".env");
            File backendEnvFile = new File("src/backend/.env");

            Dotenv dotenv = null;

            // Prioritize root .env file
            if (rootEnvFile.exists()) {
                dotenv = Dotenv.configure()
                    .directory(".")
                    .filename(".env")
                    .ignoreIfMissing()
                    .load();
                System.out.println("🚀 Early loaded .env from: ./.env");
            } else if (backendEnvFile.exists()) {
                dotenv = Dotenv.configure()
                    .directory("src/backend")
                    .filename(".env")
                    .ignoreIfMissing()
                    .load();
                System.out.println("🚀 Early loaded .env from: src/backend/.env");
            } else {
                System.out.println("⚠️  No .env file found in root or src/backend directory");
                return;
            }

            // Set system properties for Spring Boot to pick up
            if (dotenv != null) {
                dotenv.entries().forEach(entry -> {
                    String key = entry.getKey();
                    String value = entry.getValue();

                    // Set as system property for Spring Boot
                    if (System.getProperty(key) == null) {
                        System.setProperty(key, value);

                        // Debug output (hide sensitive values)
                        if (!key.toLowerCase().contains("password") &&
                            !key.toLowerCase().contains("secret") &&
                            !key.toLowerCase().contains("key") &&
                            !key.toLowerCase().contains("private")) {
                            System.out.println("📝 Loaded: " + key + " = " + value);
                        } else {
                            System.out.println("📝 Loaded: " + key + " = [HIDDEN]");
                        }
                    }
                });
            }

        } catch (Exception e) {
            System.err.println("⚠️  Warning: Could not load .env file: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
