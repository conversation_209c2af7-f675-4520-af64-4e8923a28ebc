package com.erp.aierpbackend.config;

import io.github.cdimascio.dotenv.Dotenv;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.File;

/**
 * Configuration class to load environment variables from .env file
 * This allows Spring Boot to pick up variables from .env files like Node.js applications
 */
@Configuration
public class DotenvConfig {

    @PostConstruct
    public void loadDotenv() {
        try {
            // Look for .env file - prioritize root directory
            File rootEnvFile = new File(".env");
            File backendEnvFile = new File("src/backend/.env");

            Dotenv dotenv = null;

            if (rootEnvFile.exists()) {
                // Load from root .env file (preferred)
                dotenv = Dotenv.configure()
                    .directory(".")
                    .filename(".env")
                    .ignoreIfMissing()
                    .load();
                System.out.println("✅ Loaded .env file from: ./.env");
            } else if (backendEnvFile.exists()) {
                // Load from backend-specific .env file (fallback)
                dotenv = Dotenv.configure()
                    .directory("src/backend")
                    .filename(".env")
                    .ignoreIfMissing()
                    .load();
                System.out.println("✅ Loaded .env file from: src/backend/.env");
            } else {
                System.out.println("⚠️  No .env file found. Using system environment variables only.");
                return;
            }
            
            // Set environment variables from .env file
            if (dotenv != null) {
                dotenv.entries().forEach(entry -> {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    
                    // Only set if not already set in system environment
                    if (System.getenv(key) == null) {
                        System.setProperty(key, value);
                        // For debugging - remove in production
                        if (!key.toLowerCase().contains("password") && 
                            !key.toLowerCase().contains("secret") && 
                            !key.toLowerCase().contains("key")) {
                            System.out.println("📝 Set environment variable: " + key + " = " + value);
                        } else {
                            System.out.println("📝 Set environment variable: " + key + " = [HIDDEN]");
                        }
                    }
                });
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error loading .env file: " + e.getMessage());
            // Don't fail the application startup if .env loading fails
        }
    }
}
