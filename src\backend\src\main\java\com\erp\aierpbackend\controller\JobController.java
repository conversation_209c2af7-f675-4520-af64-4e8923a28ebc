package com.erp.aierpbackend.controller;


import com.erp.aierpbackend.dto.JobDetailDTO;
import com.erp.aierpbackend.dto.JobDocumentDTO;
import com.erp.aierpbackend.dto.JobSummaryDTO;
// Removed unused imports related to old verification endpoints
// import com.erp.aierpbackend.dto.TriggerVerificationRequest;
// import com.erp.aierpbackend.dto.VerificationResultDTO;
// import com.erp.aierpbackend.entity.Discrepancy;
import com.erp.aierpbackend.entity.Job;
import com.erp.aierpbackend.entity.JobDocument;
// import com.erp.aierpbackend.entity.VerificationResult;
import com.erp.aierpbackend.repository.JobRepository;
import com.erp.aierpbackend.service.JobDocumentService;
// import jakarta.validation.Valid;
// import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
// import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.util.ArrayList; // Import ArrayList
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/jobs") // Base path for job-related endpoints
public class JobController {

    private static final Logger log = LoggerFactory.getLogger(JobController.class);

    private final JobRepository jobRepository;
    private final JobDocumentService jobDocumentService;
    // Removed RabbitTemplate injection as it's no longer used here

    @Autowired
    public JobController(JobRepository jobRepository, JobDocumentService jobDocumentService) { // Added JobDocumentService
        this.jobRepository = jobRepository;
        this.jobDocumentService = jobDocumentService;
    }

    /**
     * Get a list of all jobs with summary information.
     * TODO: Add pagination and filtering capabilities.
     * @return List of JobSummaryDTOs.
     */
    @GetMapping
    public ResponseEntity<List<JobSummaryDTO>> getAllJobs() {
        log.info("Request received for getting all job summaries");
        List<Job> jobs = jobRepository.findAll(); // Consider Pageable for large datasets
        List<JobSummaryDTO> jobSummaries = jobs.stream()
                .map(JobSummaryDTO::fromEntity)
                .collect(Collectors.toList());
        log.info("Returning {} job summaries", jobSummaries.size());
        return ResponseEntity.ok(jobSummaries);
    }

    /**
     * Get detailed information for a single job by its internal ID.
     * @param id The internal database ID of the job.
     * @return JobDetailDTO or 404 Not Found.
     */
    @GetMapping("/{id}")
    public ResponseEntity<JobDetailDTO> getJobById(@PathVariable Long id) {
        log.info("Request received for job details with internal ID: {}", id);
        return jobRepository.findById(id) // Fetch job by internal ID
                .map(job -> {
                    log.info("Found job with internal ID: {}", id);
                    // Eagerly fetch associations if needed, though DTO mapping handles it here
                    // Hibernate.initialize(job.getVerificationResult()); // Example if lazy loading issues occur
                    // if (job.getVerificationResult() != null) {
                    //     Hibernate.initialize(job.getVerificationResult().getDiscrepancies());
                    // }
                    return ResponseEntity.ok(JobDetailDTO.fromEntity(job));
                })
                .orElseGet(() -> {
                    log.warn("Job not found with internal ID: {}", id);
                    return ResponseEntity.notFound().build();
                });
    }

    /**
     * Get all documents for a specific job by its Business Central Job ID.
     * @param jobNo The Business Central Job ID.
     * @return List of JobDocumentDTOs or empty list if no documents found.
     */
    @GetMapping("/documents/{jobNo}")
    public ResponseEntity<List<JobDocumentDTO>> getJobDocuments(@PathVariable String jobNo) {
        log.info("Request received for documents for Job No: {}", jobNo);

        try {
            List<JobDocument> documents = jobDocumentService.getJobDocuments(jobNo);
            List<JobDocumentDTO> documentDTOs = documents.stream()
                    .map(JobDocumentDTO::fromEntity)
                    .collect(Collectors.toList());

            log.info("Returning {} documents for Job No: {}", documentDTOs.size(), jobNo);
            return ResponseEntity.ok(documentDTOs);

        } catch (Exception e) {
            log.error("Error fetching documents for Job No: {}: {}", jobNo, e.getMessage(), e);
            return ResponseEntity.ok(new ArrayList<>()); // Return empty list on error
        }
    }

    /**
     * Download a specific document by its ID.
     * @param documentId The document ID.
     * @return The document file as a download.
     */
    @GetMapping("/documents/download/{documentId}")
    public ResponseEntity<Resource> downloadDocument(@PathVariable Long documentId) {
        log.info("Request received for downloading document with ID: {}", documentId);

        try {
            JobDocument document = jobDocumentService.getJobDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            ByteArrayResource resource = new ByteArrayResource(document.getDocumentData());

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + document.getFileName() + "\"")
                    .contentType(MediaType.parseMediaType(document.getContentType()))
                    .contentLength(document.getDocumentData().length)
                    .body(resource);

        } catch (Exception e) {
            log.error("Error downloading document with ID: {}: {}", documentId, e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * View a specific document by its ID (inline display).
     * @param documentId The document ID.
     * @return The document file for inline viewing.
     */
    @GetMapping("/documents/view/{documentId}")
    public ResponseEntity<Resource> viewDocument(@PathVariable Long documentId) {
        log.info("Request received for viewing document with ID: {}", documentId);

        try {
            JobDocument document = jobDocumentService.getJobDocumentById(documentId)
                    .orElseThrow(() -> new RuntimeException("Document not found"));

            ByteArrayResource resource = new ByteArrayResource(document.getDocumentData());

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + document.getFileName() + "\"")
                    .contentType(MediaType.parseMediaType(document.getContentType()))
                    .contentLength(document.getDocumentData().length)
                    .body(resource);

        } catch (Exception e) {
            log.error("Error viewing document with ID: {}: {}", documentId, e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }

    // Removed old /trigger-verification endpoint (moved to VerificationController)

    // Removed old /{jobNo}/verification-result endpoint (moved to VerificationController)
}
