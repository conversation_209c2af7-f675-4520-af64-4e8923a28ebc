package com.erp.aierpbackend.dto;

import com.erp.aierpbackend.entity.JobDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for job document information to be displayed on the frontend.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobDocumentDTO {

    private Long id;
    private String jobNo;
    private String documentType;
    private String classifiedDocumentType;
    private String fileName;
    private String contentType;
    private String sourceUrl;
    private LocalDateTime createdAt;
    private long fileSizeBytes;
    private String displayName;
    private String status;
    private String comment;

    /**
     * Convert JobDocument entity to DTO.
     */
    public static JobDocumentDTO fromEntity(JobDocument document) {
        if (document == null) {
            return null;
        }

        String displayName = determineDisplayName(document);
        String status = determineStatus(document);
        String comment = determineComment(document);

        return JobDocumentDTO.builder()
                .id(document.getId())
                .jobNo(document.getJobNo())
                .documentType(document.getDocumentType())
                .classifiedDocumentType(document.getClassifiedDocumentType())
                .fileName(document.getFileName())
                .contentType(document.getContentType())
                .sourceUrl(document.getSourceUrl())
                .createdAt(document.getCreatedAt())
                .fileSizeBytes(document.getDocumentData() != null ? document.getDocumentData().length : 0)
                .displayName(displayName)
                .status(status)
                .comment(comment)
                .build();
    }

    /**
     * Determine the display name for the document.
     */
    private static String determineDisplayName(JobDocument document) {
        // If we have a classified document type, use that for display
        if (document.getClassifiedDocumentType() != null &&
            !document.getClassifiedDocumentType().equals("UNCLASSIFIED")) {

            switch (document.getClassifiedDocumentType().toUpperCase()) {
                case "SALESQUOTE":
                    return "Sales Quote";
                case "PROFORMAINVOICE":
                    return "Proforma Invoice";
                case "JOBCONSUMPTION":
                    return "Job Consumption";
                default:
                    return document.getClassifiedDocumentType();
            }
        }

        // If no classified type, try to determine from filename
        if (document.getFileName() != null) {
            String fileName = document.getFileName().toLowerCase();
            if (fileName.contains("quote")) {
                return "Sales Quote";
            } else if (fileName.contains("proforma") || fileName.contains("invoice")) {
                return "Proforma Invoice";
            } else if (fileName.contains("consumption") || fileName.contains("shipment")) {
                return "Job Consumption";
            }
        }

        // Fallback to filename or document type
        return document.getFileName() != null ? document.getFileName() : document.getDocumentType();
    }

    /**
     * Determine the status of the document.
     */
    private static String determineStatus(JobDocument document) {
        if (document.getClassifiedDocumentType() != null &&
            !document.getClassifiedDocumentType().equals("UNCLASSIFIED")) {
            return "Ready";
        } else {
            return "Available";
        }
    }

    /**
     * Determine the comment for the document.
     */
    private static String determineComment(JobDocument document) {
        if (document.getClassifiedDocumentType() != null &&
            !document.getClassifiedDocumentType().equals("UNCLASSIFIED")) {
            return "Document ready for verification";
        } else {
            return "Document downloaded from SharePoint";
        }
    }

    /**
     * Get file extension from filename.
     */
    public String getFileExtension() {
        if (fileName == null) {
            return "";
        }
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(lastDot + 1).toLowerCase() : "";
    }

    /**
     * Check if the document is a PDF.
     */
    public boolean isPdf() {
        return "pdf".equals(getFileExtension()) ||
               (contentType != null && contentType.toLowerCase().contains("pdf"));
    }

    /**
     * Check if the document is an image.
     */
    public boolean isImage() {
        String ext = getFileExtension();
        return "png".equals(ext) || "jpg".equals(ext) || "jpeg".equals(ext) || "gif".equals(ext) ||
               (contentType != null && contentType.toLowerCase().startsWith("image/"));
    }

    /**
     * Get a human-readable file size.
     */
    public String getFormattedFileSize() {
        if (fileSizeBytes < 1024) {
            return fileSizeBytes + " B";
        } else if (fileSizeBytes < 1024 * 1024) {
            return String.format("%.1f KB", fileSizeBytes / 1024.0);
        } else {
            return String.format("%.1f MB", fileSizeBytes / (1024.0 * 1024.0));
        }
    }
}
