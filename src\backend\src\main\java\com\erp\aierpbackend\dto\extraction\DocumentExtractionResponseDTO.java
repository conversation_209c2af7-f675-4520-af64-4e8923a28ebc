package com.erp.aierpbackend.dto.extraction;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO representing the response from the LLM document extraction service.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentExtractionResponseDTO {
    
    @JsonProperty("documents")
    private List<ExtractedDocumentDTO> documents;
    
    @JsonProperty("raw_llm_response")
    private String rawLlmResponse;
    
    @JsonProperty("error_message")
    private String errorMessage;
}
