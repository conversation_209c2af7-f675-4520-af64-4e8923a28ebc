package com.erp.aierpbackend.dto.extraction;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO representing a document with all its extracted data.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtractedDocumentDTO {
    
    @JsonProperty("document_type")
    private String documentType;
    
    @JsonProperty("confidence")
    private Double confidence;
    
    @JsonProperty("sales_quote_data")
    private ExtractedSalesQuoteDataDTO salesQuoteData;
    
    @JsonProperty("proforma_invoice_data")
    private ExtractedProformaInvoiceDataDTO proformaInvoiceData;
    
    @JsonProperty("job_consumption_data")
    private ExtractedJobConsumptionDataDTO jobConsumptionData;
    
    /**
     * Get the document identifier based on document type.
     */
    public String getDocumentIdentifier() {
        switch (documentType) {
            case "SalesQuote":
                return salesQuoteData != null ? salesQuoteData.getSalesQuoteNumber() : null;
            case "ProformaInvoice":
                return proformaInvoiceData != null ? proformaInvoiceData.getTaxInvoiceNumber() : null;
            case "JobConsumption":
                return jobConsumptionData != null ? jobConsumptionData.getJobShipmentNumber() : null;
            default:
                return null;
        }
    }
    
    /**
     * Get the customer account number from any document type.
     */
    public String getCustomerAccountNumber() {
        if (salesQuoteData != null) {
            return salesQuoteData.getCustomerAccountNumber();
        } else if (proformaInvoiceData != null) {
            return proformaInvoiceData.getCustomerAccountNumber();
        }
        return null;
    }
    
    /**
     * Get the customer name from any document type.
     */
    public String getCustomerName() {
        if (salesQuoteData != null) {
            return salesQuoteData.getCustomerName();
        } else if (proformaInvoiceData != null) {
            return proformaInvoiceData.getCustomerName();
        }
        return null;
    }
}
