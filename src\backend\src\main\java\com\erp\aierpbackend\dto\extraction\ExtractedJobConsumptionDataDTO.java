package com.erp.aierpbackend.dto.extraction;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO representing data extracted from a Job Consumption document.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtractedJobConsumptionDataDTO {
    
    @JsonProperty("job_number")
    private String jobNumber;
    
    @JsonProperty("job_shipment_number")
    private String jobShipmentNumber;
    
    @JsonProperty("received_by_signature_present")
    private Boolean receivedBySignaturePresent;
    
    @JsonProperty("received_by_name")
    private String receivedByName;
    
    @JsonProperty("line_items")
    private List<ExtractedLineItemDTO> lineItems;
}
