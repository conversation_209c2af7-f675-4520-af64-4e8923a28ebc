package com.erp.aierpbackend.dto.extraction;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO representing a line item extracted from a document.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtractedLineItemDTO {
    
    @JsonProperty("line_number")
    private Integer lineNumber;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("quantity")
    private String quantity;
    
    @JsonProperty("unit_price")
    private String unitPrice;
    
    @JsonProperty("total_price")
    private String totalPrice;
    
    @JsonProperty("item_number")
    private String itemNumber;
    
    @JsonProperty("type")
    private String type;
}
