package com.erp.aierpbackend.dto.extraction;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO representing data extracted from a Proforma Invoice document.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtractedProformaInvoiceDataDTO {
    
    @JsonProperty("tax_invoice_number")
    private String taxInvoiceNumber;
    
    @JsonProperty("customer_account_number")
    private String customerAccountNumber;
    
    @JsonProperty("customer_name")
    private String customerName;
    
    @JsonProperty("amount")
    private String amount;
    
    @JsonProperty("invoice_date")
    private String invoiceDate;
    
    @JsonProperty("line_items")
    private List<ExtractedLineItemDTO> lineItems;
}
