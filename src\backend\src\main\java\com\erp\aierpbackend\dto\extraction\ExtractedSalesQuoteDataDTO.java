package com.erp.aierpbackend.dto.extraction;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO representing data extracted from a Sales Quote document.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtractedSalesQuoteDataDTO {
    
    @JsonProperty("sales_quote_number")
    private String salesQuoteNumber;
    
    @JsonProperty("customer_account_number")
    private String customerAccountNumber;
    
    @JsonProperty("customer_name")
    private String customerName;
    
    @JsonProperty("total_amount_including_vat")
    private String totalAmountIncludingVat;
    
    @JsonProperty("quote_date")
    private String quoteDate;
    
    @JsonProperty("line_items")
    private List<ExtractedLineItemDTO> lineItems;
}
