package com.erp.aierpbackend.dto.verification;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * DTO representing the comprehensive verification result for a job.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComprehensiveVerificationResultDTO {

    private String jobNo;
    private String overallStatus; // SUCCESS, FAILED, PARTIAL_SUCCESS
    private List<VerificationDiscrepancyDTO> allDiscrepancies;
    private List<VerificationStepResultDTO> stepResults;
    private Map<String, String> extractedIdentifiers;
    private String verificationSummary;
    private LocalDateTime verificationTimestamp;
    private Long totalExecutionTimeMs;
    private String verifiedBy; // "AI LLM Service"

    /**
     * Determine overall status based on discrepancies.
     */
    public void determineOverallStatus() {
        if (allDiscrepancies == null || allDiscrepancies.isEmpty()) {
            this.overallStatus = "SUCCESS";
        } else {
            // Check if there are any HIGH severity discrepancies
            boolean hasHighSeverity = allDiscrepancies.stream()
                    .anyMatch(d -> "HIGH".equals(d.getSeverity()));

            if (hasHighSeverity) {
                this.overallStatus = "FAILED";
            } else {
                this.overallStatus = "PARTIAL_SUCCESS";
            }
        }
    }

    /**
     * Generate verification summary.
     */
    public void generateSummary() {
        if (allDiscrepancies == null || allDiscrepancies.isEmpty()) {
            this.verificationSummary = "All documents verified successfully with no discrepancies found.";
        } else {
            this.verificationSummary = String.format(
                "Verification completed with %d discrepancies found.",
                allDiscrepancies.size()
            );
        }
    }

    /**
     * Check if verification was successful (no high severity discrepancies).
     */
    public boolean isSuccessful() {
        return "SUCCESS".equals(overallStatus) || "PARTIAL_SUCCESS".equals(overallStatus);
    }
}
