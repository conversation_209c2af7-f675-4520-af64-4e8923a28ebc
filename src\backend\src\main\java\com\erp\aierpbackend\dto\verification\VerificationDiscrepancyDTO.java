package com.erp.aierpbackend.dto.verification;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO representing a verification discrepancy found during document verification.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationDiscrepancyDTO {
    
    private String discrepancyType; // CROSS_DOCUMENT, BC_MISMATCH, MISSING_FIELD, BUSINESS_RULE, etc.
    private String fieldName;
    private String expectedValue;
    private String actualValue;
    private String severity; // HIGH, MEDIUM, LOW
    private String description;
    private String documentType; // Which document this discrepancy relates to
    private Double confidence; // Confidence in this discrepancy finding
    
    /**
     * Create a cross-document discrepancy.
     */
    public static VerificationDiscrepancyDTO createCrossDocumentDiscrepancy(
            String fieldName, String expectedValue, String actualValue, String description) {
        return VerificationDiscrepancyDTO.builder()
                .discrepancyType("CROSS_DOCUMENT")
                .fieldName(fieldName)
                .expectedValue(expectedValue)
                .actualValue(actualValue)
                .severity("HIGH")
                .description(description)
                .confidence(1.0)
                .build();
    }
    
    /**
     * Create a Business Central mismatch discrepancy.
     */
    public static VerificationDiscrepancyDTO createBCMismatchDiscrepancy(
            String documentType, String fieldName, String expectedValue, String actualValue, String description) {
        return VerificationDiscrepancyDTO.builder()
                .discrepancyType("BC_MISMATCH")
                .documentType(documentType)
                .fieldName(fieldName)
                .expectedValue(expectedValue)
                .actualValue(actualValue)
                .severity("MEDIUM")
                .description(description)
                .confidence(1.0)
                .build();
    }
    
    /**
     * Create a missing field discrepancy.
     */
    public static VerificationDiscrepancyDTO createMissingFieldDiscrepancy(
            String documentType, String fieldName, String description) {
        return VerificationDiscrepancyDTO.builder()
                .discrepancyType("MISSING_FIELD")
                .documentType(documentType)
                .fieldName(fieldName)
                .severity("HIGH")
                .description(description)
                .confidence(1.0)
                .build();
    }
    
    /**
     * Create a business rule violation discrepancy.
     */
    public static VerificationDiscrepancyDTO createBusinessRuleDiscrepancy(
            String documentType, String fieldName, String description, String severity) {
        return VerificationDiscrepancyDTO.builder()
                .discrepancyType("BUSINESS_RULE")
                .documentType(documentType)
                .fieldName(fieldName)
                .severity(severity)
                .description(description)
                .confidence(1.0)
                .build();
    }
}
