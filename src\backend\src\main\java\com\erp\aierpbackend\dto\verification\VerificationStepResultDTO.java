package com.erp.aierpbackend.dto.verification;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO representing the result of a specific verification step.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationStepResultDTO {
    
    private String stepName;
    private String status; // SUCCESS, FAILED, SKIPPED
    private List<VerificationDiscrepancyDTO> discrepancies;
    private String message;
    private Long executionTimeMs;
    
    /**
     * Create a successful step result.
     */
    public static VerificationStepResultDTO success(String stepName, String message) {
        return VerificationStepResultDTO.builder()
                .stepName(stepName)
                .status("SUCCESS")
                .message(message)
                .build();
    }
    
    /**
     * Create a failed step result.
     */
    public static VerificationStepResultDTO failed(String stepName, String message, 
                                                  List<VerificationDiscrepancyDTO> discrepancies) {
        return VerificationStepResultDTO.builder()
                .stepName(stepName)
                .status("FAILED")
                .message(message)
                .discrepancies(discrepancies)
                .build();
    }
    
    /**
     * Create a skipped step result.
     */
    public static VerificationStepResultDTO skipped(String stepName, String message) {
        return VerificationStepResultDTO.builder()
                .stepName(stepName)
                .status("SKIPPED")
                .message(message)
                .build();
    }
}
