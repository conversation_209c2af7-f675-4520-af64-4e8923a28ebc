package com.erp.aierpbackend.service;

import com.erp.aierpbackend.dto.extraction.DocumentExtractionResponseDTO;
import com.erp.aierpbackend.dto.extraction.ExtractedDocumentDTO;
import com.erp.aierpbackend.entity.JobDocument;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.*;

/**
 * Service for extracting comprehensive document data using the LLM as an expert OCR system.
 */
@Service
@Slf4j
public class DocumentDataExtractionService {

    private final WebClient webClient;

    @Value("${llm.python.service.baseurl}")
    private String llmServiceBaseUrl;

    public DocumentDataExtractionService(WebClient.Builder webClientBuilder,
                                       @Value("${llm.python.service.baseurl}") String llmServiceBaseUrl) {
        this.llmServiceBaseUrl = llmServiceBaseUrl;
        this.webClient = webClientBuilder.baseUrl(llmServiceBaseUrl).build();
        log.info("DocumentDataExtractionService initialized. LLM service base URL: {}", llmServiceBaseUrl);
    }

    /**
     * Extract all document data from job documents using the new comprehensive LLM endpoint.
     *
     * @param jobNo The job number
     * @param documents List of job documents
     * @return DocumentExtractionResponseDTO containing all extracted data
     */
    public DocumentExtractionResponseDTO extractAllDocumentData(String jobNo, List<JobDocument> documents) {
        log.info("Starting comprehensive document data extraction for Job No: {}", jobNo);

        if (documents == null || documents.isEmpty()) {
            log.warn("No documents provided for extraction. Job No: {}", jobNo);
            return DocumentExtractionResponseDTO.builder()
                    .documents(Collections.emptyList())
                    .errorMessage("No documents provided for extraction")
                    .build();
        }

        try {
            // Prepare the request payload
            Map<String, Object> requestPayload = new HashMap<>();
            requestPayload.put("job_no", jobNo);

            // Convert documents to base64 images
            List<Map<String, String>> documentImages = new ArrayList<>();
            for (JobDocument document : documents) {
                if (document.getDocumentData() != null && document.getDocumentData().length > 0) {
                    String mimeType = determineMimeType(document.getFileName());
                    String base64Data = Base64.getEncoder().encodeToString(document.getDocumentData());

                    log.info("Processing document for extraction - Job No: {}, File: {}, Content Type: {}, MIME Type: {}, Data Size: {} bytes, Base64 Size: {} chars",
                            jobNo, document.getFileName(), document.getContentType(), mimeType,
                            document.getDocumentData().length, base64Data.length());

                    Map<String, String> imageData = new HashMap<>();
                    imageData.put("image_base64", base64Data);
                    imageData.put("mime_type", mimeType);
                    documentImages.add(imageData);
                } else {
                    log.warn("Document {} has no data for Job No: {}", document.getFileName(), jobNo);
                }
            }

            if (documentImages.isEmpty()) {
                log.warn("No valid document images found for Job No: {}", jobNo);
                return DocumentExtractionResponseDTO.builder()
                        .documents(Collections.emptyList())
                        .errorMessage("No valid document images found")
                        .build();
            }

            requestPayload.put("document_images", documentImages);

            // Make the API call to the new comprehensive extraction endpoint
            log.info("Calling LLM extraction service at: /extract_all_document_data for Job No: {} with {} document images",
                    jobNo, documentImages.size());

            DocumentExtractionResponseDTO extractionResponse = webClient.post()
                    .uri("/extract_all_document_data")
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestPayload)
                    .retrieve()
                    .bodyToMono(DocumentExtractionResponseDTO.class)
                    .block();
            if (extractionResponse == null) {
                log.error("Received null response from LLM extraction service for Job No: {}", jobNo);
                return DocumentExtractionResponseDTO.builder()
                        .documents(Collections.emptyList())
                        .errorMessage("Received null response from LLM service")
                        .build();
            }

            int documentCount = extractionResponse.getDocuments() != null ? extractionResponse.getDocuments().size() : 0;
            log.info("Successfully extracted data from {} documents for Job No: {}", documentCount, jobNo);

            if (extractionResponse.getErrorMessage() != null) {
                log.warn("LLM extraction service returned error message for Job No: {}: {}", jobNo, extractionResponse.getErrorMessage());
            }

            if (documentCount == 0) {
                log.warn("LLM extraction service returned zero documents for Job No: {}. Raw response length: {}",
                        jobNo, extractionResponse.getRawLlmResponse() != null ? extractionResponse.getRawLlmResponse().length() : 0);
                if (extractionResponse.getRawLlmResponse() != null && extractionResponse.getRawLlmResponse().length() > 0) {
                    log.debug("Raw LLM response for Job No: {}: {}", jobNo,
                            extractionResponse.getRawLlmResponse().length() > 1000 ?
                            extractionResponse.getRawLlmResponse().substring(0, 1000) + "..." :
                            extractionResponse.getRawLlmResponse());
                }
            }

            // Log extracted identifiers for debugging
            if (extractionResponse.getDocuments() != null) {
                for (ExtractedDocumentDTO doc : extractionResponse.getDocuments()) {
                    String identifier = doc.getDocumentIdentifier();
                    if (identifier != null) {
                        log.info("Extracted {} identifier: {} for Job No: {}",
                                doc.getDocumentType(), identifier, jobNo);
                    }
                }
            }

            return extractionResponse;

        } catch (Exception e) {
            log.error("Error during comprehensive document data extraction for Job No: {}: {}", jobNo, e.getMessage(), e);
            return DocumentExtractionResponseDTO.builder()
                    .documents(Collections.emptyList())
                    .errorMessage("Error during document data extraction: " + e.getMessage())
                    .build();
        }
    }

    /**
     * Determine MIME type based on file extension.
     */
    private String determineMimeType(String fileName) {
        if (fileName == null) {
            return "application/octet-stream";
        }

        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFileName.endsWith(".gif")) {
            return "image/gif";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * Validate that required identifiers are present in the extracted data.
     */
    public Map<String, String> validateAndExtractIdentifiers(DocumentExtractionResponseDTO extractionResponse) {
        Map<String, String> identifiers = new HashMap<>();

        if (extractionResponse.getDocuments() == null) {
            return identifiers;
        }

        for (ExtractedDocumentDTO doc : extractionResponse.getDocuments()) {
            String identifier = doc.getDocumentIdentifier();
            if (identifier != null && !identifier.trim().isEmpty()) {
                switch (doc.getDocumentType()) {
                    case "SalesQuote":
                        identifiers.put("salesQuoteNo", identifier);
                        break;
                    case "ProformaInvoice":
                        identifiers.put("proformaInvoiceNo", identifier);
                        break;
                    case "JobConsumption":
                        identifiers.put("jobConsumptionNo", identifier);
                        break;
                }
            }
        }

        return identifiers;
    }
}
