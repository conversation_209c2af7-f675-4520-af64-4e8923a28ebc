package com.erp.aierpbackend.service;

import com.erp.aierpbackend.dto.verification.ComprehensiveVerificationResultDTO;
import com.erp.aierpbackend.entity.Job;
import com.erp.aierpbackend.entity.JobDocument;
import com.erp.aierpbackend.entity.VerificationRequest;
import com.erp.aierpbackend.repository.JobRepository;
import com.erp.aierpbackend.repository.VerificationRequestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Service for orchestrating document processing in separate transactions.
 * This service ensures that document download, classification, and verification
 * each run in their own transaction to prevent transaction-related issues.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DocumentProcessingOrchestratorService {

    private final JobAttachmentService jobAttachmentService;
    private final DocumentClassificationService documentClassificationService;
    private final JobDocumentVerificationService jobDocumentVerificationService;
    private final JobDocumentService jobDocumentService;
    private final VerificationRequestRepository verificationRequestRepository;
    private final JobRepository jobRepository;
    private final ActivityLogService activityLogService;

    /**
     * Orchestrates the document download and classification process.
     * This method is called from the afterCommit callback in VerificationProcessingService.
     * It coordinates the entire document processing workflow but ensures each phase completes
     * fully before the next phase begins, avoiding transaction isolation issues.
     *
     * The revised flow ensures all documents are processed and their details (sales quote number,
     * invoice number) are returned before the Business Central checks begin.
     *
     * @param verificationRequestId The verification request ID
     * @param jobNo The job number
     */
    public void orchestrateDocumentProcessing(String verificationRequestId, String jobNo) {
        log.info("Starting document processing orchestration for Job No: {}", jobNo);

        try {
            // Phase 1: Download documents in a new transaction and wait for it to complete
            log.info("Starting Phase 1: Document download for Job No: {}", jobNo);
            downloadDocumentsInNewTransaction(verificationRequestId, jobNo);

            // Add a small delay to ensure the transaction is fully committed
            try {
                log.info("Waiting for download transaction to fully commit for Job No: {}", jobNo);
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupted while waiting for download transaction to commit: {}", e.getMessage());
            }

            // Phase 2: Extract document identifiers in a new transaction
            // This step processes all documents and extracts key identifiers before any Business Central checks
            log.info("Starting Phase 2: Document identifier extraction for Job No: {}", jobNo);
            extractDocumentIdentifiersInNewTransaction(verificationRequestId, jobNo);

            // Add a small delay to ensure the transaction is fully committed
            try {
                log.info("Waiting for extraction transaction to fully commit for Job No: {}", jobNo);
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupted while waiting for extraction transaction to commit: {}", e.getMessage());
            }

            // Phase 3: Comprehensive verification in a new transaction
            log.info("Starting Phase 3: Comprehensive verification for Job No: {}", jobNo);
            performComprehensiveVerificationInNewTransaction(verificationRequestId, jobNo);

        } catch (Exception e) {
            log.error("Error during document processing orchestration for Job No: {}: {}", jobNo, e.getMessage(), e);
            handleProcessingError(verificationRequestId, jobNo, e);
        }
    }

    /**
     * Downloads documents from SharePoint in a new transaction.
     * This method is called from orchestrateDocumentProcessing and starts a new transaction.
     *
     * @param verificationRequestId The verification request ID
     * @param jobNo The job number
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void downloadDocumentsInNewTransaction(String verificationRequestId, String jobNo) {
        log.info("Starting document download in a new transaction for Job No: {}", jobNo);

        VerificationRequest request = verificationRequestRepository.findById(verificationRequestId).orElse(null);
        if (request == null) {
            log.error("VerificationRequest not found for ID: {} during document download. Cannot proceed.", verificationRequestId);
            return;
        }

        try {
            log.info("Starting document download from SharePoint for Job No: {}", jobNo);
            try {
                // Use a longer timeout for document download (3 minutes)
                List<String> downloadedDocumentTypes = jobAttachmentService.fetchAndStoreJobAttachments(jobNo)
                        .collectList()
                        .block(java.time.Duration.ofMinutes(3)); // Explicit timeout of 3 minutes

                if (downloadedDocumentTypes != null && !downloadedDocumentTypes.isEmpty()) {
                    log.info("Successfully downloaded {} documents from SharePoint for Job No: {}: {}",
                            downloadedDocumentTypes.size(), jobNo, downloadedDocumentTypes);
                } else {
                    log.warn("No documents were downloaded from SharePoint for Job No: {}", jobNo);
                }

                log.info("Document download from SharePoint completed for Job No: {}", jobNo);
            } catch (Exception e) {
                log.error("Error downloading documents from SharePoint for Job No: {}: {}", jobNo, e.getMessage(), e);
                // Continue with next steps even if download fails
            }
        } catch (Exception e) {
            log.error("Error during document download for Job No: {}: {}", jobNo, e.getMessage(), e);
            // Log error but don't rethrow - allow process to continue
        }
    }

    /**
     * Legacy method kept for backward compatibility.
     * @deprecated Use downloadDocumentsInNewTransaction instead
     */
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void downloadDocuments(String verificationRequestId, String jobNo) {
        log.warn("Using deprecated downloadDocuments method - should use downloadDocumentsInNewTransaction");
        downloadDocumentsInNewTransaction(verificationRequestId, jobNo);
    }

    /**
     * Classifies documents for a job in a new transaction.
     * This method is called from orchestrateDocumentProcessing and starts a new transaction.
     *
     * @param verificationRequestId The verification request ID
     * @param jobNo The job number
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void classifyDocumentsInNewTransaction(String verificationRequestId, String jobNo) {
        log.info("Starting document classification in a new transaction for Job No: {}", jobNo);

        VerificationRequest request = verificationRequestRepository.findById(verificationRequestId).orElse(null);
        if (request == null) {
            log.error("VerificationRequest not found for ID: {} during document classification. Cannot proceed.", verificationRequestId);
            return;
        }

        try {
            // Add classification step with blocking call and timeout
            log.info("Starting document classification for Job No: {}", jobNo);
            try {
                // Use the blocking method with a 2-minute timeout
                Map<Long, String> classificationResults = documentClassificationService.classifyJobDocumentsBlocking(jobNo, 120);

                log.info("Document classification completed for Job No: {}", jobNo);

                if (classificationResults != null && !classificationResults.isEmpty()) {
                    log.info("Successfully classified {} documents for Job No: {}",
                            classificationResults.size(), jobNo);
                } else {
                    log.warn("No documents were classified for Job No: {}", jobNo);
                }
            } catch (Exception e) {
                log.error("Error classifying documents for Job No: {}: {}", jobNo, e.getMessage(), e);
                // Continue with next steps even if classification fails
            }
        } catch (Exception e) {
            log.error("Error during document classification for Job No: {}: {}", jobNo, e.getMessage(), e);
            // Log error but don't rethrow - allow process to continue
        }
    }

    /**
     * Legacy method kept for backward compatibility.
     * @deprecated Use classifyDocumentsInNewTransaction instead
     */
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void classifyDocuments(String verificationRequestId, String jobNo) {
        log.warn("Using deprecated classifyDocuments method - should use classifyDocumentsInNewTransaction");
        classifyDocumentsInNewTransaction(verificationRequestId, jobNo);
    }

    /**
     * Extracts document identifiers (sales quote number, invoice number) from all documents in a new transaction.
     * This method is called from orchestrateDocumentProcessing and starts a new transaction.
     * It processes all documents and extracts key identifiers before any Business Central checks.
     *
     * @param verificationRequestId The verification request ID
     * @param jobNo The job number
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void extractDocumentIdentifiersInNewTransaction(String verificationRequestId, String jobNo) {
        log.info("Starting document identifier extraction in a new transaction for Job No: {}", jobNo);

        VerificationRequest request = verificationRequestRepository.findById(verificationRequestId).orElse(null);
        if (request == null) {
            log.error("VerificationRequest not found for ID: {} during document identifier extraction. Cannot proceed.", verificationRequestId);
            return;
        }

        Job job = jobRepository.findByBusinessCentralJobId(jobNo).orElse(null);
        if (job == null) {
            log.error("Job not found for JobNo: {} during document identifier extraction. Cannot proceed.", jobNo);
            return;
        }

        try {
            // Check if documents exist in the database
            List<JobDocument> documents = jobDocumentService.getJobDocuments(jobNo);
            if (documents.isEmpty()) {
                log.warn("No documents found in database during extraction phase for Job No: {}. This may indicate a transaction isolation issue.", jobNo);

                // Try to re-fetch documents from SharePoint if needed
                log.info("Attempting to re-fetch documents from SharePoint for Job No: {}", jobNo);
                List<String> downloadedDocumentTypes = jobAttachmentService.fetchAndStoreJobAttachments(jobNo)
                        .collectList()
                        .block(java.time.Duration.ofMinutes(3)); // Explicit timeout of 3 minutes

                if (downloadedDocumentTypes == null || downloadedDocumentTypes.isEmpty()) {
                    log.error("Failed to download documents from SharePoint during extraction phase for Job No: {}", jobNo);
                    updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.FAILED,
                            Job.JobStatus.ERROR, List.of("Failed to download documents from SharePoint"),
                            "Document extraction failed: No documents available");
                    return;
                }

                log.info("Successfully re-fetched {} documents from SharePoint for Job No: {}: {}",
                        downloadedDocumentTypes.size(), jobNo, downloadedDocumentTypes);

                // Add a small delay to ensure the documents are fully committed
                try {
                    log.info("Waiting for re-fetched documents to be fully committed for Job No: {}", jobNo);
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Interrupted while waiting for documents to commit: {}", e.getMessage());
                }
            }

            // DEPRECATED: This method is part of the old architecture
            log.warn("DEPRECATED: extractDocumentIdentifiersInNewTransaction called for Job No: {}. This method is deprecated.", jobNo);

            // Return early since this method is deprecated
            updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.FAILED,
                    Job.JobStatus.ERROR, List.of("DEPRECATED: This verification method is no longer supported. Please use the new comprehensive verification."),
                    "Deprecated method called - use new comprehensive verification");
            return;
        } catch (Exception e) {
            log.error("Error during document identifier extraction for Job No: {}: {}",
                    jobNo, e.getMessage(), e);
            updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.FAILED,
                    Job.JobStatus.ERROR, List.of("Error during document identifier extraction: " + e.getMessage()),
                    "Document identifier extraction failed: " + e.getMessage());
        }
    }

    /**
     * Verifies documents for a job using the extracted identifiers in a new transaction.
     * This method is called from orchestrateDocumentProcessing and starts a new transaction.
     * It uses the previously extracted document identifiers to fetch Business Central data
     * and complete the verification process.
     *
     * @param verificationRequestId The verification request ID
     * @param jobNo The job number
     */








    /**
     * Handles errors during document processing with enhanced error classification.
     *
     * @param verificationRequestId The verification request ID
     * @param jobNo The job number
     * @param e The exception that occurred
     */
    private void handleProcessingError(String verificationRequestId, String jobNo, Exception e) {
        try {
            VerificationRequest request = verificationRequestRepository.findById(verificationRequestId).orElse(null);
            Job job = jobRepository.findByBusinessCentralJobId(jobNo).orElse(null);

            if (request != null && job != null) {
                // Check if this is a business logic error (missing identifiers) or system error
                if (e instanceof SystemErrorHandler.BusinessLogicErrorException) {
                    // Business logic error - show to user
                    String businessErrorMsg = e.getMessage();
                    log.info("Business logic error for Job No: {}: {}", jobNo, businessErrorMsg);
                    updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.FAILED,
                            Job.JobStatus.FLAGGED, List.of(businessErrorMsg), businessErrorMsg);
                } else if (e instanceof SystemErrorHandler.SystemErrorException) {
                    // System error - don't show details to user, retry will be handled by SystemErrorHandler
                    log.error("System error for Job No: {}: {}", jobNo, e.getMessage());
                    updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.FAILED,
                            Job.JobStatus.ERROR, List.of("System temporarily unavailable - processing will retry automatically"),
                            "System error: " + e.getMessage());
                } else {
                    // Unknown error type - treat as system error
                    String errorMsg = "Error during document processing: " + e.getMessage();
                    log.error("Unknown error type for Job No: {}: {}", jobNo, e.getMessage(), e);
                    updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.FAILED,
                            Job.JobStatus.ERROR, List.of(errorMsg), e.getMessage());
                }
            }
        } catch (Exception ex) {
            log.error("Error handling processing error for Job No: {}: {}", jobNo, ex.getMessage(), ex);
        }
    }

    /**
     * Helper method to update statuses and log activity.
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateRequestAndJobStatus(VerificationRequest request, Job job,
                                       VerificationRequest.VerificationStatus requestStatus, Job.JobStatus jobStatus,
                                       List<String> discrepancies, String activityLogReason) {
        try {
            log.info("Updating verification request status to {} and job status to {} for Job No: {}",
                    requestStatus, jobStatus, request.getJobNo());

            request.setStatus(requestStatus);
            request.setResultTimestamp(LocalDateTime.now());
            request.setDiscrepanciesJson(serializeDiscrepancies(discrepancies));
            verificationRequestRepository.save(request);

            String activityLogEvent = ActivityLogService.EVENT_JOB_PROCESSED; // Default
            Long jobIdForLog = null;

            // Store the verification result in the Job entity
            if (job != null && (requestStatus == VerificationRequest.VerificationStatus.COMPLETED ||
                               requestStatus == VerificationRequest.VerificationStatus.FAILED)) {
                // Store the verification result
                job.setVerificationResult(activityLogReason);

                // Set hasDiscrepancies flag
                if (discrepancies != null && !discrepancies.isEmpty()) {
                    job.setHasDiscrepancies(true);
                } else {
                    job.setHasDiscrepancies(false);
                }
            }

            if (job != null && jobStatus != null) {
                job.setStatus(jobStatus);
                job.setLastProcessedAt(LocalDateTime.now());
                jobRepository.save(job);
                jobIdForLog = job.getId(); // Use job ID if available

                log.info("Successfully updated Job No: {} to status: {} at {}",
                        request.getJobNo(), jobStatus, LocalDateTime.now());
            }

            // Determine Activity Log event type based on status
            if (requestStatus == VerificationRequest.VerificationStatus.FAILED || (jobStatus != null && jobStatus == Job.JobStatus.ERROR)) {
                activityLogEvent = ActivityLogService.EVENT_ERROR;
            } else if (jobStatus != null && jobStatus == Job.JobStatus.FLAGGED) {
                activityLogEvent = ActivityLogService.EVENT_JOB_PROCESSED; // Keep as processed, but reason indicates flagged
            } else if (jobStatus != null && jobStatus == Job.JobStatus.SKIPPED) {
                activityLogEvent = ActivityLogService.EVENT_JOB_PROCESSED; // Keep as processed, reason indicates skipped
            }

            // Log activity
            if (activityLogReason != null) {
                String messageFormat = "Verification [ID: %s] %s for Job No: %s. Reason: %s";
                String statusText = requestStatus.toString().toLowerCase();
                if (requestStatus == VerificationRequest.VerificationStatus.COMPLETED && jobStatus == Job.JobStatus.VERIFIED) {
                    statusText = "successful";
                    messageFormat = "Verification [ID: %s] %s for Job No: %s"; // Simpler message for success
                    activityLogService.recordActivity(activityLogEvent,
                            String.format(messageFormat, request.getId(), statusText, request.getJobNo()),
                            jobIdForLog, "System");
                } else {
                    activityLogService.recordActivity(activityLogEvent,
                            String.format(messageFormat, request.getId(), statusText, request.getJobNo(), activityLogReason),
                            jobIdForLog, "System");
                }
            }
        } catch (Exception e) {
            log.error("Error updating request and job status: {}", e.getMessage(), e);
        }
    }

    /**
     * Helper method to serialize discrepancies to JSON.
     */
    private String serializeDiscrepancies(List<String> discrepancies) {
        if (discrepancies == null || discrepancies.isEmpty()) {
            return "[]";
        }

        try {
            return new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(discrepancies);
        } catch (Exception e) {
            log.error("Error serializing discrepancies: {}", e.getMessage(), e);
            return "[]";
        }
    }

    /**
     * NEW ARCHITECTURE: Orchestrates comprehensive document verification using the enhanced architecture.
     * This method uses the new LLM-as-OCR approach with Java-based verification logic.
     *
     * @param verificationRequestId The verification request ID
     * @param jobNo The job number
     */
    public void orchestrateComprehensiveDocumentVerification(String verificationRequestId, String jobNo) {
        log.info("Starting comprehensive document verification orchestration for Job No: {} using new architecture", jobNo);

        try {
            // Phase 1: Download documents if needed (same as before)
            log.info("Starting Phase 1: Document download for Job No: {}", jobNo);
            downloadDocumentsInNewTransaction(verificationRequestId, jobNo);

            // Phase 2: Perform comprehensive verification using the new architecture
            log.info("Starting Phase 2: Comprehensive verification for Job No: {}", jobNo);
            performComprehensiveVerificationInNewTransaction(verificationRequestId, jobNo);

        } catch (Exception e) {
            log.error("Error during comprehensive document verification orchestration for Job No: {}: {}", jobNo, e.getMessage(), e);
            handleProcessingError(verificationRequestId, jobNo, e);
        }
    }

    /**
     * Performs comprehensive document verification in a new transaction using the new architecture.
     *
     * @param verificationRequestId The verification request ID
     * @param jobNo The job number
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void performComprehensiveVerificationInNewTransaction(String verificationRequestId, String jobNo) {
        log.info("Starting comprehensive verification in a new transaction for Job No: {}", jobNo);

        VerificationRequest request = verificationRequestRepository.findById(verificationRequestId).orElse(null);
        if (request == null) {
            log.error("VerificationRequest not found for ID: {} during comprehensive verification. Cannot proceed.", verificationRequestId);
            return;
        }

        Job job = jobRepository.findByBusinessCentralJobId(jobNo).orElse(null);
        if (job == null) {
            log.error("Job not found for JobNo: {} during comprehensive verification. Cannot proceed.", jobNo);
            return;
        }

        try {
            // Use the new comprehensive verification method
            ComprehensiveVerificationResultDTO verificationResult =
                jobDocumentVerificationService.performComprehensiveDocumentVerification(jobNo);

            // Convert the comprehensive result to the format expected by updateRequestAndJobStatus
            List<String> discrepancies = convertVerificationResultToDiscrepancies(verificationResult);

            // Update status based on verification results
            if ("SUCCESS".equals(verificationResult.getOverallStatus())) {
                updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.COMPLETED,
                        Job.JobStatus.VERIFIED, Collections.emptyList(), verificationResult.getVerificationSummary());
                log.info("Comprehensive verification completed successfully for Job No: {}", jobNo);
            } else if ("PARTIAL_SUCCESS".equals(verificationResult.getOverallStatus())) {
                updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.COMPLETED,
                        Job.JobStatus.FLAGGED, discrepancies, verificationResult.getVerificationSummary());
                log.info("Comprehensive verification completed with warnings for Job No: {}", jobNo);
            } else {
                // For FAILED status, use only the actual discrepancies without duplicating the summary
                List<String> finalDiscrepancies = new ArrayList<>(discrepancies);

                // Ensure we have at least one discrepancy for failed verification
                if (finalDiscrepancies.isEmpty()) {
                    finalDiscrepancies.add("Verification failed - no specific details available");
                }
                updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.COMPLETED,
                        Job.JobStatus.FLAGGED, finalDiscrepancies, verificationResult.getVerificationSummary());
                log.info("Comprehensive verification completed with discrepancies for Job No: {}", jobNo);
            }

        } catch (Exception e) {
            log.error("Error during comprehensive verification for Job No: {}: {}", jobNo, e.getMessage(), e);
            updateRequestAndJobStatus(request, job, VerificationRequest.VerificationStatus.FAILED,
                    Job.JobStatus.ERROR, List.of("Error during comprehensive verification: " + e.getMessage()),
                    "Comprehensive verification failed: " + e.getMessage());
        }
    }

    /**
     * Convert ComprehensiveVerificationResultDTO to a list of discrepancy strings.
     */
    private List<String> convertVerificationResultToDiscrepancies(ComprehensiveVerificationResultDTO verificationResult) {
        if (verificationResult.getAllDiscrepancies() == null || verificationResult.getAllDiscrepancies().isEmpty()) {
            return Collections.emptyList();
        }

        return verificationResult.getAllDiscrepancies().stream()
                .map(d -> d.getDescription())
                .collect(java.util.stream.Collectors.toList());
    }
}
