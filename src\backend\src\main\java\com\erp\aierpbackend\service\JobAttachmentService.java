package com.erp.aierpbackend.service;

import com.erp.aierpbackend.entity.JobDocument;
import com.erp.aierpbackend.repository.JobDocumentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * Service for handling job attachments from Business Central and SharePoint.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class JobAttachmentService {

    private final BusinessCentralService businessCentralService;
    private final SharePointService sharePointService;
    private final JobDocumentService jobDocumentService;
    private final JobDocumentRepository jobDocumentRepository;

    // Document type constants
    public static final String SALES_QUOTE_TYPE = "SalesQuote";
    public static final String PROFORMA_INVOICE_TYPE = "ProformaInvoice";
    public static final String JOB_CONSUMPTION_TYPE = "JobConsumption";
    public static final String UNCLASSIFIED_TYPE = "UNCLASSIFIED";

    /**
     * Fetches job attachments from Business Central and downloads them from SharePoint.
     * Stores the documents in the database.
     *
     * @param jobNo The job number
     * @return Flux of downloaded document types
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public Flux<String> fetchAndStoreJobAttachments(String jobNo) {
        log.info("Fetching and storing job attachments for Job No: '{}'", jobNo);

        // Special handling for job J069026 to diagnose issues
        if ("J069026".equals(jobNo)) {
            log.error("SPECIAL DIAGNOSTIC: Detected problematic job J069026");

            // First check if the job exists in Business Central
            try {
                var jobListEntry = businessCentralService.fetchJobListEntry(jobNo).block();
                if (jobListEntry == null) {
                    log.error("SPECIAL DIAGNOSTIC: Job J069026 does not exist in Business Central");
                } else {
                    log.error("SPECIAL DIAGNOSTIC: Job J069026 exists in Business Central: {}", jobListEntry);
                }
            } catch (Exception e) {
                log.error("SPECIAL DIAGNOSTIC: Error checking if Job J069026 exists: {}", e.getMessage(), e);
            }
        }

        // Log transaction information if possible
        try {
            log.debug("Transaction active: {}, Transaction name: {}",
                    TransactionSynchronizationManager.isActualTransactionActive(),
                    TransactionSynchronizationManager.getCurrentTransactionName());
        } catch (Exception e) {
            log.debug("Could not log transaction details: {}", e.getMessage());
        }

        // No need for delay with proper transaction management
        log.debug("Fetching attachments with proper transaction management");

        // Check which documents already exist in the database
        boolean salesQuoteExists = jobDocumentService.documentExists(jobNo, SALES_QUOTE_TYPE);
        boolean proformaInvoiceExists = jobDocumentService.documentExists(jobNo, PROFORMA_INVOICE_TYPE);
        boolean jobConsumptionExists = jobDocumentService.documentExists(jobNo, JOB_CONSUMPTION_TYPE);

        // If all required documents already exist, no need to fetch from SharePoint
        if (salesQuoteExists && proformaInvoiceExists && jobConsumptionExists) {
            log.info("All required documents already exist in database for Job No: {}", jobNo);
            return Flux.just(SALES_QUOTE_TYPE, PROFORMA_INVOICE_TYPE, JOB_CONSUMPTION_TYPE);
        }

        log.info("Fetching attachment links from Business Central for Job No: {}", jobNo);
        return businessCentralService.fetchJobAttachmentLinks(jobNo)
                .doOnSuccess(links -> {
                    if (links == null) {
                        log.error("Business Central returned null for JobAttachmentLinks for Job No: {}", jobNo);

                        // Special handling for job J069026
                        if ("J069026".equals(jobNo)) {
                            log.error("SPECIAL DIAGNOSTIC: Business Central returned null for JobAttachmentLinks for problematic job J069026");
                        }
                    } else {
                        log.info("Business Central returned JobAttachmentLinks for Job No: {}, File_Links: '{}'",
                                jobNo, links.getFileLinks());

                        // Special handling for job J069026
                        if ("J069026".equals(jobNo)) {
                            log.error("SPECIAL DIAGNOSTIC: Business Central returned JobAttachmentLinks for problematic job J069026: {}", links);
                        }
                    }
                })
                .doOnError(e -> {
                    log.error("Error fetching JobAttachmentLinks from Business Central for Job No: {}: {}",
                            jobNo, e.getMessage(), e);

                    // Special handling for job J069026
                    if ("J069026".equals(jobNo)) {
                        log.error("SPECIAL DIAGNOSTIC: Error fetching JobAttachmentLinks for problematic job J069026: {}", e.getMessage(), e);
                    }
                })
                .flatMapMany(attachmentLinks -> {
                    if (attachmentLinks == null) {
                        log.error("No attachment links found for Job No: {}. Business Central returned null.", jobNo);

                        // Special handling for job J069026
                        if ("J069026".equals(jobNo)) {
                            log.error("SPECIAL DIAGNOSTIC: No attachment links found for problematic job J069026");
                        }

                        return Flux.empty();
                    }

                    String[] sharePointUrls = attachmentLinks.getSharePointUrls();
                    if (sharePointUrls.length == 0) {
                        log.error("No SharePoint URLs found in attachment links for Job No: {}. Raw File_Links: '{}'",
                                jobNo, attachmentLinks.getFileLinks());

                        // Special handling for job J069026
                        if ("J069026".equals(jobNo)) {
                            log.error("SPECIAL DIAGNOSTIC: No SharePoint URLs found for problematic job J069026. Raw File_Links: '{}'",
                                    attachmentLinks.getFileLinks());
                        }

                        return Flux.empty();
                    }

                    log.info("Found {} SharePoint URLs for Job No: {}: {}", sharePointUrls.length, jobNo, String.join(", ", sharePointUrls));

                    // Log the raw File_Links field for debugging
                    log.info("Raw File_Links field for Job No: {}: {}", jobNo, attachmentLinks.getFileLinks());

                    // Special handling for job J069026
                    if ("J069026".equals(jobNo)) {
                        log.error("SPECIAL DIAGNOSTIC: Found {} SharePoint URLs for problematic job J069026: {}",
                                sharePointUrls.length, String.join(", ", sharePointUrls));
                    }

                    // Clean up the URLs - remove any that are obviously invalid
                    List<String> validUrls = new ArrayList<>();
                    for (String url : sharePointUrls) {
                        if (url != null && !url.trim().isEmpty() &&
                            (url.contains("sharepoint.com") || url.contains("dayliff") || url.contains("http"))) {

                            // Fix specific URL encoding issues
                            String cleanUrl = url.trim();

                            // Fix double-encoded spaces (%2520 instead of %20)
                            if (cleanUrl.contains("%2520")) {
                                cleanUrl = cleanUrl.replace("%2520", "%20");
                                log.info("Fixed double-encoded spaces in URL: {} -> {}", url, cleanUrl);
                            }

                            // Fix specific issue with jOB%20SHIPMENT_124.pdf
                            if (cleanUrl.contains("jOB%2520SHIPMENT_124.pdf")) {
                                cleanUrl = cleanUrl.replace("jOB%2520SHIPMENT_124.pdf", "jOB%20SHIPMENT_124.pdf");
                                log.info("Fixed URL encoding for job shipment document: {}", cleanUrl);
                            }

                            // Log the URL for debugging
                            log.debug("Processing SharePoint URL: {}", cleanUrl);

                            validUrls.add(cleanUrl);
                            log.debug("Added valid URL: {}", cleanUrl);
                        } else {
                            log.warn("Filtered out invalid SharePoint URL for Job No: {}: '{}'", jobNo, url);
                        }
                    }

                    if (validUrls.isEmpty()) {
                        log.warn("No valid SharePoint URLs found after filtering for Job No: {}", jobNo);
                        return Flux.empty();
                    }

                    log.info("After filtering, found {} valid SharePoint URLs for Job No: {}: {}",
                            validUrls.size(), jobNo, String.join(", ", validUrls));

                    return Flux.fromIterable(validUrls)
                            .map(url -> {
                                log.debug("Processing SharePoint URL for Job No: {}: {}", jobNo, url);
                                return url;
                            })
                            .flatMap(url -> downloadAndStoreDocument(jobNo, url));
                })
                .onErrorResume(e -> {
                    log.error("Error fetching and storing job attachments for Job No: {}", jobNo, e);
                    return Flux.error(new RuntimeException("Failed to fetch and store job attachments for job " + jobNo, e));
                });
    }

    /**
     * Downloads a document from SharePoint and stores it in the database.
     *
     * @param jobNo The job number
     * @param sharePointUrl The SharePoint URL
     * @return Mono containing the document type
     */
    private Mono<String> downloadAndStoreDocument(String jobNo, String sharePointUrl) {
        log.info("Processing document from SharePoint URL: {} for Job No: {}", sharePointUrl, jobNo);

        // Special handling for job J069023 and the problematic file
        if (jobNo.equals("J069023") && sharePointUrl.contains("jOB%20SHIPMENT_124.pdf")) {
            log.info("Special handling for job J069023 and file jOB%20SHIPMENT_124.pdf");
        }

        // Special handling for job J069026
        if ("J069026".equals(jobNo)) {
            log.error("SPECIAL DIAGNOSTIC: Attempting to download document for problematic job J069026 from URL: {}", sharePointUrl);
        }

        final String fileName = extractFileName(sharePointUrl);
        log.debug("Extracted file name: {} for Job No: {}", fileName, jobNo);

        // Store all documents as UNCLASSIFIED initially
        final String documentType = UNCLASSIFIED_TYPE;
        log.info("Storing document as UNCLASSIFIED for file: {} (Job No: {})", fileName, jobNo);

        // Check if a document with the same job number and filename already exists (regardless of type)
        boolean fileExists = jobDocumentRepository.existsByJobNoAndFileName(jobNo, fileName);

        if (fileExists) {
            log.info("Document with filename {} already exists for Job No: {}", fileName, jobNo);

            // Get the existing document
            Optional<JobDocument> existingDocOpt = jobDocumentRepository.findTopByJobNoAndFileNameOrderByIdDesc(jobNo, fileName);

            if (existingDocOpt.isPresent()) {
                JobDocument existingDoc = existingDocOpt.get();
                log.info("Found existing document with ID: {}, Document Type: '{}', Classified Type: '{}' for Job No: '{}', File Name: '{}'",
                        existingDoc.getId(), existingDoc.getDocumentType(), existingDoc.getClassifiedDocumentType(), jobNo, fileName);

                // If the document is already classified, return its classified type
                if (existingDoc.getClassifiedDocumentType() != null && !existingDoc.getClassifiedDocumentType().isEmpty()) {
                    log.info("Existing document is already classified as '{}', skipping download", existingDoc.getClassifiedDocumentType());
                    return Mono.just(existingDoc.getClassifiedDocumentType());
                }

                // If the document is not classified, return UNCLASSIFIED
                log.info("Existing document is not classified, skipping download");
                return Mono.just(documentType);
            } else {
                // This should not happen since we checked existsByJobNoAndFileName, but just in case
                log.warn("Document existence check returned true but document not found for Job No: '{}', File Name: '{}'", jobNo, fileName);
                // Continue with download to be safe
            }
        }

        log.info("Downloading document of type {} from SharePoint for Job No: {}", documentType, jobNo);

        return sharePointService.downloadFile(sharePointUrl)
                .flatMap(fileData -> {
                    log.info("Successfully downloaded document from SharePoint for Job No: '{}', Document Type: '{}', size: {} bytes",
                            jobNo, documentType, fileData.length);

                    JobDocument document = JobDocument.builder()
                            .jobNo(jobNo)
                            .documentType(documentType)
                            .fileName(fileName)
                            .contentType(determineContentType(fileName))
                            .documentData(fileData)
                            .sourceUrl(sharePointUrl)
                            .build();

                    log.debug("Created JobDocument entity for Job No: '{}', Document Type: '{}', File Name: '{}', Content Type: '{}'",
                            jobNo, documentType, fileName, determineContentType(fileName));

                    return Mono.fromCallable(() -> {
                        log.debug("About to save JobDocument for Job No: '{}', Document Type: '{}'", jobNo, documentType);
                        JobDocument saved = jobDocumentService.saveJobDocument(document);
                        log.debug("JobDocument saved successfully with ID: {} for Job No: '{}', Document Type: '{}'",
                                saved.getId(), jobNo, documentType);

                        // No need for delay with proper transaction management
                        log.debug("Document saved with proper transaction management");

                        return saved;
                    })
                    .thenReturn(documentType);
                })
                .onErrorResume(e -> {
                    // Log detailed error information for all download failures
                    log.error("Error downloading document from SharePoint URL: {} for Job No: {}", sharePointUrl, jobNo, e);
                    log.error("Detailed error information: Type={}, Message={}", e.getClass().getName(), e.getMessage());

                    // Log the stack trace for detailed debugging
                    StringBuilder stackTrace = new StringBuilder();
                    for (StackTraceElement element : e.getStackTrace()) {
                        stackTrace.append("\n    at ").append(element.toString());
                    }
                    log.error("Stack trace for failed download: {}", stackTrace.toString());

                    // Return empty to continue with other URLs
                    return Mono.empty();
                });
    }

    /**
     * Extracts the file name from a SharePoint URL.
     *
     * @param sharePointUrl The SharePoint URL
     * @return The file name
     */
    private String extractFileName(String sharePointUrl) {
        try {
            // Handle URLs with query parameters
            String urlPath = sharePointUrl;
            if (urlPath.contains("?")) {
                urlPath = urlPath.substring(0, urlPath.indexOf("?"));
            }

            // Get the last segment of the URL path
            String[] segments = urlPath.split("/");
            if (segments.length > 0) {
                String lastSegment = segments[segments.length - 1];
                // URL decode the file name
                return java.net.URLDecoder.decode(lastSegment, "UTF-8");
            }

            // Fallback to Paths.get if the above approach fails
            Path path = Paths.get(urlPath);
            return path.getFileName().toString();
        } catch (Exception e) {
            log.warn("Error extracting file name from URL: {}. Using fallback method.", sharePointUrl, e);
            // Fallback: Use the last part of the URL as the file name
            String[] parts = sharePointUrl.split("/");
            return parts.length > 0 ? parts[parts.length - 1] : "unknown_file";
        }
    }



    /**
     * Determines the content type based on the file name.
     *
     * @param fileName The file name
     * @return The content type
     */
    private String determineContentType(String fileName) {
        String lowerCaseFileName = fileName.toLowerCase();
        if (lowerCaseFileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerCaseFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerCaseFileName.endsWith(".jpg") || lowerCaseFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        }
        return "application/octet-stream";
    }
}
