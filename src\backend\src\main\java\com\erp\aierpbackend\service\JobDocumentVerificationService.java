package com.erp.aierpbackend.service;


import com.erp.aierpbackend.dto.extraction.DocumentExtractionResponseDTO;
import com.erp.aierpbackend.dto.verification.ComprehensiveVerificationResultDTO;
import com.erp.aierpbackend.entity.JobDocument;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Service for verifying job documents using the new comprehensive architecture.
 * This service uses the LLM as an expert OCR system and performs all verification logic in Java.
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Transactional(propagation = Propagation.REQUIRED)
public class JobDocumentVerificationService {

    private final BusinessCentralService businessCentralService;
    private final JobDocumentService jobDocumentService;
    private final JobAttachmentService jobAttachmentService;

    // New services for the enhanced architecture
    private final DocumentDataExtractionService documentDataExtractionService;
    private final VerificationEngine verificationEngine;

    /**
     * NEW ARCHITECTURE: Comprehensive document verification using the enhanced architecture.
     * This method uses the LLM as an expert OCR system and performs all verification logic in Java.
     *
     * @param jobNo The job number
     * @return ComprehensiveVerificationResultDTO containing detailed verification results
     */
    public ComprehensiveVerificationResultDTO performComprehensiveDocumentVerification(String jobNo) {
        log.info("Starting comprehensive document verification for Job No: {} using new architecture", jobNo);

        try {
            // Step 1: Get all documents for the job
            List<JobDocument> allDocuments = jobDocumentService.getJobDocuments(jobNo);

            // If no documents exist, try to fetch from SharePoint
            if (allDocuments.isEmpty()) {
                log.info("No documents found in database for Job No: {}. Fetching from SharePoint...", jobNo);
                try {
                    List<String> downloadedDocumentTypes = jobAttachmentService.fetchAndStoreJobAttachments(jobNo)
                            .collectList()
                            .block();

                    if (downloadedDocumentTypes == null || downloadedDocumentTypes.isEmpty()) {
                        log.error("No documents were downloaded from SharePoint for Job No: {}", jobNo);
                        return ComprehensiveVerificationResultDTO.builder()
                                .jobNo(jobNo)
                                .overallStatus("FAILED")
                                .verificationSummary("No documents could be downloaded from SharePoint")
                                .allDiscrepancies(Collections.emptyList())
                                .stepResults(Collections.emptyList())
                                .extractedIdentifiers(Collections.emptyMap())
                                .verificationTimestamp(LocalDateTime.now())
                                .verifiedBy("AI LLM Service")
                                .build();
                    }

                    // Refresh the document list after downloading
                    allDocuments = jobDocumentService.getJobDocuments(jobNo);
                } catch (Exception e) {
                    log.error("Error downloading documents from SharePoint for Job No: {}: {}", jobNo, e.getMessage());
                    return ComprehensiveVerificationResultDTO.builder()
                            .jobNo(jobNo)
                            .overallStatus("FAILED")
                            .verificationSummary("Error downloading documents from SharePoint: " + e.getMessage())
                            .allDiscrepancies(Collections.emptyList())
                            .stepResults(Collections.emptyList())
                            .extractedIdentifiers(Collections.emptyMap())
                            .verificationTimestamp(LocalDateTime.now())
                            .verifiedBy("AI LLM Service")
                            .build();
                }
            }

            if (allDocuments.isEmpty()) {
                log.error("No documents available for verification for Job No: {}", jobNo);
                return ComprehensiveVerificationResultDTO.builder()
                        .jobNo(jobNo)
                        .overallStatus("FAILED")
                        .verificationSummary("No documents available for verification")
                        .allDiscrepancies(Collections.emptyList())
                        .stepResults(Collections.emptyList())
                        .extractedIdentifiers(Collections.emptyMap())
                        .verificationTimestamp(LocalDateTime.now())
                        .verifiedBy("AI LLM Service")
                        .build();
            }

            // Step 2: Extract all document data using the new LLM service
            log.info("Extracting document data for {} documents for Job No: {}", allDocuments.size(), jobNo);
            DocumentExtractionResponseDTO extractionResponse = documentDataExtractionService.extractAllDocumentData(jobNo, allDocuments);

            // Step 3: Perform comprehensive verification using the VerificationEngine
            log.info("Performing comprehensive verification for Job No: {}", jobNo);
            ComprehensiveVerificationResultDTO verificationResult = verificationEngine.performComprehensiveVerification(jobNo, extractionResponse);

            // Step 4: Update Business Central based on verification results
            updateBusinessCentralWithVerificationResults(jobNo, verificationResult);

            log.info("Comprehensive verification completed for Job No: {} with status: {}",
                    jobNo, verificationResult.getOverallStatus());

            return verificationResult;

        } catch (Exception e) {
            log.error("Error during comprehensive document verification for Job No: {}: {}", jobNo, e.getMessage(), e);
            return ComprehensiveVerificationResultDTO.builder()
                    .jobNo(jobNo)
                    .overallStatus("FAILED")
                    .verificationSummary("System error during verification: " + e.getMessage())
                    .allDiscrepancies(Collections.emptyList())
                    .stepResults(Collections.emptyList())
                    .extractedIdentifiers(Collections.emptyMap())
                    .verificationTimestamp(LocalDateTime.now())
                    .verifiedBy("AI LLM Service")
                    .build();
        }
    }

    /**
     * Update Business Central with verification results.
     */
    private void updateBusinessCentralWithVerificationResults(String jobNo, ComprehensiveVerificationResultDTO verificationResult) {
        try {
            String verificationComment;
            boolean isSuccess = verificationResult.isSuccessful();

            if (isSuccess) {
                verificationComment = "Verified by AI LLM Service - " + verificationResult.getVerificationSummary();
            } else {
                // Include discrepancy details in the comment
                StringBuilder commentBuilder = new StringBuilder("Verification failed - ");
                commentBuilder.append(verificationResult.getVerificationSummary());

                if (verificationResult.getAllDiscrepancies() != null && !verificationResult.getAllDiscrepancies().isEmpty()) {
                    commentBuilder.append(" Discrepancies: ");
                    verificationResult.getAllDiscrepancies().stream()
                            .limit(3) // Limit to first 3 discrepancies to avoid comment being too long
                            .forEach(d -> commentBuilder.append(d.getDescription()).append("; "));
                }

                verificationComment = commentBuilder.toString();
                if (verificationComment.length() > 250) {
                    verificationComment = verificationComment.substring(0, 247) + "...";
                }
            }

            // Update Business Central using only ETag method
            try {
                if (isSuccess) {
                    businessCentralService.updateAllVerificationFields(jobNo, verificationComment, true).block();
                } else {
                    businessCentralService.updateAllVerificationFields(jobNo, verificationComment, false).block();
                }
                log.info("Successfully updated Business Central verification fields for Job No: {}", jobNo);
            } catch (Exception updateException) {
                log.error("Failed to update Business Central verification fields for Job No: {}: {}",
                        jobNo, updateException.getMessage());
                // Don't throw exception, just log the error - verification result is still saved in our database
            }

        } catch (Exception e) {
            log.error("Failed to update Business Central with verification results for Job No: {}: {}",
                    jobNo, e.getMessage(), e);
            // Don't throw exception here as verification was completed, just BC update failed
        }
    }


}
