package com.erp.aierpbackend.service;

import com.erp.aierpbackend.dto.gemini.DocumentClassificationResult;
import com.erp.aierpbackend.dto.gemini.GeminiVerificationResult; // Reusing DTO, consider renaming later

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;


import java.io.IOException; // Keep for method signature, though WebClient uses reactive exceptions
import java.time.Duration;
import java.util.Base64;


import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LLMProxyService {

    private final WebClient webClient;
    private final SystemErrorHandler systemErrorHandler;

    @Value("${llm.python.service.baseurl}") // e.g., http://gemini-python-service:8000
    private String llmServiceBaseUrl;



    public LLMProxyService(WebClient.Builder webClientBuilder,
                           SystemErrorHandler systemErrorHandler,
                           @Value("${llm.python.service.baseurl}") String llmServiceBaseUrl) {
        this.webClient = webClientBuilder.baseUrl(llmServiceBaseUrl).build();
        this.systemErrorHandler = systemErrorHandler;
        this.llmServiceBaseUrl = llmServiceBaseUrl; // Ensure it's set if used directly
        log.info("LLMProxyService initialized. Python service base URL: {}", this.llmServiceBaseUrl);
    }

    // Inner classes for request payloads to Python service
    private static class DocumentImagePayload {
        public String image_base64;
        public String mime_type;

        public DocumentImagePayload(String image_base64, String mime_type) {
            this.image_base64 = image_base64;
            this.mime_type = mime_type;
        }
    }





    // Request payload for document verification
    private static class VerificationRequestPayload {
        public String job_no;
        public String document_type;
        public List<DocumentImagePayload> document_images;
        public Map<String, Object> erp_data;

        public VerificationRequestPayload(String job_no, String document_type, List<DocumentImagePayload> document_images, Map<String, Object> erp_data) {
            this.job_no = job_no;
            this.document_type = document_type;
            this.document_images = document_images;
            this.erp_data = erp_data;
        }
    }

    // Request payload for document classification
    private static class ClassificationRequestPayload {
        public String job_no;
        public List<DocumentImagePayload> document_images;

        public ClassificationRequestPayload(String job_no, List<DocumentImagePayload> document_images) {
            this.job_no = job_no;
            this.document_images = document_images;
        }
    }




    /**
     * Classifies a document using the LLM service.
     *
     * @param jobNo The job number
     * @param documentImageBytesList List of document images as byte arrays
     * @return Mono containing the classification result
     */
    public Mono<DocumentClassificationResult> classifyDocument(
            String jobNo,
            List<byte[]> documentImageBytesList
    ) {
        log.info("LLMProxyService: Starting document classification for Job No: '{}'", jobNo);

        if (documentImageBytesList == null || documentImageBytesList.isEmpty()) {
            log.warn("No document images provided for classification. Job No: '{}'", jobNo);
            return Mono.error(new IOException("No document images provided for classification"));
        }

        List<DocumentImagePayload> imagePayloads = documentImageBytesList.stream()
                .map(bytes -> new DocumentImagePayload(Base64.getEncoder().encodeToString(bytes), "image/png"))
                .collect(Collectors.toList());

        ClassificationRequestPayload payload = new ClassificationRequestPayload(jobNo, imagePayloads);

        return systemErrorHandler.withSystemErrorRetry(
                webClient.post()
                        .uri("/classify_document")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(payload)
                        .retrieve()
                        .bodyToMono(DocumentClassificationResult.class)
                        .timeout(Duration.ofSeconds(30)),
                "LLM document classification"
        ).onErrorResume(e -> {
            if (systemErrorHandler.isSystemError(e)) {
                // System error - create fallback response without exposing details
                log.error("System error during LLM document classification for job '{}': {}", jobNo, e.getMessage());
                DocumentClassificationResult fallback = new DocumentClassificationResult();
                fallback.setDocumentType("UNKNOWN");
                fallback.setConfidence(0.0);
                fallback.setReasoning("System temporarily unavailable");
                return Mono.just(fallback);
            } else {
                // Business logic error - propagate with details
                log.error("Business logic error during LLM document classification for job '{}': {}", jobNo, e.getMessage());
                return Mono.error(new SystemErrorHandler.BusinessLogicErrorException(
                        "Document classification failed: " + e.getMessage(), e));
            }
        });
    }

    public GeminiVerificationResult verifyDocument(
            String jobNo,
            String documentType,
            List<byte[]> documentImageBytesList,
            Map<String, Object> erpData
    ) throws IOException { // Keep IOException for now
        log.info("LLMProxyService: Starting verification for Job No: {}, Document Type: {}", jobNo, documentType);

        if (documentImageBytesList == null || documentImageBytesList.isEmpty()) {
            log.warn("No document images provided for verification. Job No: {}, Document Type: {}", jobNo, documentType);
            return new GeminiVerificationResult(); // Or throw
        }

        List<DocumentImagePayload> imagePayloads = documentImageBytesList.stream()
                .map(bytes -> new DocumentImagePayload(Base64.getEncoder().encodeToString(bytes), "image/png")) // Assuming PNG
                .collect(Collectors.toList());

        VerificationRequestPayload payload = new VerificationRequestPayload(jobNo, documentType, imagePayloads, erpData);

        try {
            // The Python service is expected to return a JSON compatible with GeminiVerificationResult
            GeminiVerificationResult response = webClient.post()
                    .uri("/verify_document")
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(payload)
                    .retrieve()
                    .bodyToMono(GeminiVerificationResult.class) // Deserialize directly to this DTO
                    .timeout(Duration.ofSeconds(60)) // Add 60-second timeout for verification
                    .block(Duration.ofSeconds(65)); // Blocking with timeout

            if (response != null) {
                // Check for an error message field if your Python service adds one to GeminiVerificationResult structure
                // For example, if GeminiVerificationResult had an 'errorMessage' field:
                // if (response.getErrorMessage() != null && !response.getErrorMessage().isEmpty()) {
                //    log.error("Error from LLM service (verify_document) for job {}: {}", jobNo, response.getErrorMessage());
                //    throw new IOException("LLM service error: " + response.getErrorMessage());
                // }
                return response;
            } else {
                log.error("No response from LLM service (verify_document) for job {}", jobNo);
                throw new IOException("No response from LLM service for document verification.");
            }
        } catch (Exception e) {
            log.error("Error calling LLM service for document verification (job {}): {}", jobNo, e.getMessage(), e);
            throw new IOException("Failed to call LLM service for document verification: " + e.getMessage(), e);
        }
    }

    /**
     * DEPRECATED: This method is part of the old architecture and should not be used.
     * Use the new DocumentDataExtractionService and VerificationEngine instead.
     */
    @Deprecated
    public Object classifyAndVerifyDocument(
            String jobNo,
            List<byte[]> documentImageBytesList,
            Map<String, Object> erpData
    ) throws IOException {
        log.warn("DEPRECATED METHOD CALLED: classifyAndVerifyDocument for Job No: {}. Use the new comprehensive verification architecture instead.", jobNo);
        throw new UnsupportedOperationException("This method is deprecated. Use the new comprehensive verification architecture instead.");
    }

    /**
     * DEPRECATED: This method is part of the old architecture and should not be used.
     * Use the new DocumentDataExtractionService instead.
     */
    @Deprecated
    public Object batchExtractDocumentIdentifiers(
            String jobNo,
            List<byte[]> documentImagesBytesList
    ) throws IOException {
        log.warn("DEPRECATED METHOD CALLED: batchExtractDocumentIdentifiers for Job No: {}. Use the new DocumentDataExtractionService instead.", jobNo);
        throw new UnsupportedOperationException("This method is deprecated. Use the new DocumentDataExtractionService instead.");
    }

    /**
     * DEPRECATED: This method is part of the old architecture and should not be used.
     * Use the new DocumentDataExtractionService instead.
     */
    @Deprecated
    public Map<String, String> extractDocumentIdentifiers(
            String jobNo,
            String documentType,
            List<byte[]> documentImagesBytesList
    ) throws IOException {
        log.warn("DEPRECATED METHOD CALLED: extractDocumentIdentifiers for Job No: {}. Use the new DocumentDataExtractionService instead.", jobNo);
        throw new UnsupportedOperationException("This method is deprecated. Use the new DocumentDataExtractionService instead.");
    }
}
