package com.erp.aierpbackend.service;

import com.erp.aierpbackend.dto.dynamics.JobLedgerEntryDTO;
import com.erp.aierpbackend.dto.dynamics.SalesInvoiceDTO;
import com.erp.aierpbackend.dto.dynamics.SalesQuoteDTO;
import com.erp.aierpbackend.dto.extraction.DocumentExtractionResponseDTO;
import com.erp.aierpbackend.dto.extraction.ExtractedDocumentDTO;
import com.erp.aierpbackend.dto.verification.ComprehensiveVerificationResultDTO;
import com.erp.aierpbackend.dto.verification.VerificationDiscrepancyDTO;
import com.erp.aierpbackend.dto.verification.VerificationStepResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Comprehensive verification engine that performs all document verification logic.
 * This replaces the LLM-based verification with pure Java business logic.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VerificationEngine {

    private final BusinessCentralService businessCentralService;

    /**
     * Perform comprehensive verification of extracted document data.
     */
    public ComprehensiveVerificationResultDTO performComprehensiveVerification(
            String jobNo, DocumentExtractionResponseDTO extractionResponse) {

        log.info("Starting comprehensive verification for Job No: {}", jobNo);
        long startTime = System.currentTimeMillis();

        ComprehensiveVerificationResultDTO result = ComprehensiveVerificationResultDTO.builder()
                .jobNo(jobNo)
                .allDiscrepancies(new ArrayList<>())
                .stepResults(new ArrayList<>())
                .extractedIdentifiers(new HashMap<>())
                .verificationTimestamp(LocalDateTime.now())
                .verifiedBy("AI LLM Service")
                .build();

        // Step 1: Validate extraction response
        VerificationStepResultDTO extractionValidation = validateExtractionResponse(extractionResponse);
        result.getStepResults().add(extractionValidation);
        if ("FAILED".equals(extractionValidation.getStatus())) {
            result.getAllDiscrepancies().addAll(extractionValidation.getDiscrepancies());
            result.setTotalExecutionTimeMs(System.currentTimeMillis() - startTime);
            result.determineOverallStatus();
            result.generateSummary();
            return result;
        }

        // Step 1.5: Validate required document types are present
        VerificationStepResultDTO documentTypeValidation = validateRequiredDocumentTypes(extractionResponse);
        result.getStepResults().add(documentTypeValidation);
        if ("FAILED".equals(documentTypeValidation.getStatus())) {
            result.getAllDiscrepancies().addAll(documentTypeValidation.getDiscrepancies());
            // FAIL FAST: Cannot proceed without all required document types
            result.setTotalExecutionTimeMs(System.currentTimeMillis() - startTime);
            result.determineOverallStatus();
            result.generateSummary();
            return result;
        }

        // Step 2: Extract and validate identifiers
        VerificationStepResultDTO identifierValidation = validateDocumentIdentifiers(extractionResponse, result);
        result.getStepResults().add(identifierValidation);
        if ("FAILED".equals(identifierValidation.getStatus())) {
            result.getAllDiscrepancies().addAll(identifierValidation.getDiscrepancies());
            // FAIL FAST: Cannot proceed without all required identifiers
            result.setTotalExecutionTimeMs(System.currentTimeMillis() - startTime);
            result.determineOverallStatus();
            result.generateSummary();
            return result;
        }

        // Step 3: Cross-document validation
        VerificationStepResultDTO crossDocValidation = performCrossDocumentValidation(extractionResponse);
        result.getStepResults().add(crossDocValidation);
        if ("FAILED".equals(crossDocValidation.getStatus())) {
            result.getAllDiscrepancies().addAll(crossDocValidation.getDiscrepancies());
            // FAIL FAST: Cannot proceed with inconsistent documents
            result.setTotalExecutionTimeMs(System.currentTimeMillis() - startTime);
            result.determineOverallStatus();
            result.generateSummary();
            return result;
        }

        // Step 4: Business Central data retrieval and validation
        VerificationStepResultDTO bcValidation = performBusinessCentralValidation(jobNo, extractionResponse, result);
        result.getStepResults().add(bcValidation);
        if ("FAILED".equals(bcValidation.getStatus())) {
            result.getAllDiscrepancies().addAll(bcValidation.getDiscrepancies());
            // FAIL FAST: Cannot proceed without Business Central validation
            result.setTotalExecutionTimeMs(System.currentTimeMillis() - startTime);
            result.determineOverallStatus();
            result.generateSummary();
            return result;
        }

        // Step 5: Business rules validation
        VerificationStepResultDTO businessRulesValidation = performBusinessRulesValidation(extractionResponse);
        result.getStepResults().add(businessRulesValidation);
        if ("FAILED".equals(businessRulesValidation.getStatus())) {
            result.getAllDiscrepancies().addAll(businessRulesValidation.getDiscrepancies());
        }

        // Finalize result
        result.setTotalExecutionTimeMs(System.currentTimeMillis() - startTime);
        result.determineOverallStatus();
        result.generateSummary();

        log.info("Comprehensive verification completed for Job No: {} with status: {}",
                jobNo, result.getOverallStatus());

        return result;
    }

    /**
     * Step 1: Validate that the extraction response is valid and contains documents.
     */
    private VerificationStepResultDTO validateExtractionResponse(DocumentExtractionResponseDTO extractionResponse) {
        List<VerificationDiscrepancyDTO> discrepancies = new ArrayList<>();

        if (extractionResponse == null) {
            discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                    "SYSTEM", "extraction_response", "Extraction response is null", "HIGH"));
            return VerificationStepResultDTO.failed("Extraction Response Validation",
                    "Extraction response is null", discrepancies);
        }

        if (extractionResponse.getErrorMessage() != null && !extractionResponse.getErrorMessage().trim().isEmpty()) {
            discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                    "SYSTEM", "extraction_error", extractionResponse.getErrorMessage(), "HIGH"));
            return VerificationStepResultDTO.failed("Extraction Response Validation",
                    "LLM extraction failed: " + extractionResponse.getErrorMessage(), discrepancies);
        }

        if (extractionResponse.getDocuments() == null || extractionResponse.getDocuments().isEmpty()) {
            discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                    "SYSTEM", "no_documents", "No documents were extracted from the images", "HIGH"));
            return VerificationStepResultDTO.failed("Extraction Response Validation",
                    "No documents extracted", discrepancies);
        }

        return VerificationStepResultDTO.success("Extraction Response Validation",
                "Extraction response is valid with " + extractionResponse.getDocuments().size() + " documents");
    }



    /**
     * Step 1.5: Validate that all required document types are present.
     * PREREQUISITE: Extraction response must be valid
     */
    private VerificationStepResultDTO validateRequiredDocumentTypes(DocumentExtractionResponseDTO extractionResponse) {
        List<VerificationDiscrepancyDTO> discrepancies = new ArrayList<>();

        // Required document types
        Set<String> requiredTypes = Set.of("SalesQuote", "ProformaInvoice", "JobConsumption");
        Set<String> foundTypes = new HashSet<>();

        // Collect all document types found
        for (ExtractedDocumentDTO doc : extractionResponse.getDocuments()) {
            if (doc.getDocumentType() != null) {
                foundTypes.add(doc.getDocumentType());
            }
        }

        // Check for missing document types
        for (String requiredType : requiredTypes) {
            if (!foundTypes.contains(requiredType)) {
                String userFriendlyName = getUserFriendlyDocumentTypeName(requiredType);
                discrepancies.add(VerificationDiscrepancyDTO.createMissingFieldDiscrepancy(
                        "SYSTEM", requiredType,
                        String.format("Missing required document: %s", userFriendlyName)));
            }
        }

        if (!discrepancies.isEmpty()) {
            return VerificationStepResultDTO.failed("Required Document Types Validation",
                    String.format("Missing %d required document types", discrepancies.size()), discrepancies);
        }

        return VerificationStepResultDTO.success("Required Document Types Validation",
                "All required document types are present: Sales Quote, Proforma Invoice, Job Consumption");
    }

    /**
     * Step 2: Validate that required document identifiers are present.
     * PREREQUISITE: All required document types must be present
     */
    private VerificationStepResultDTO validateDocumentIdentifiers(
            DocumentExtractionResponseDTO extractionResponse, ComprehensiveVerificationResultDTO result) {

        List<VerificationDiscrepancyDTO> discrepancies = new ArrayList<>();
        Map<String, String> identifiers = new HashMap<>();

        for (ExtractedDocumentDTO doc : extractionResponse.getDocuments()) {
            String identifier = doc.getDocumentIdentifier();
            String docType = doc.getDocumentType();

            if (identifier == null || identifier.trim().isEmpty()) {
                String fieldName = getIdentifierFieldName(docType);
                discrepancies.add(VerificationDiscrepancyDTO.createMissingFieldDiscrepancy(
                        docType, fieldName,
                        String.format("Cannot find %s from %s document", fieldName, docType)));
            } else {
                // Store identifier for later use
                switch (docType) {
                    case "SalesQuote":
                        identifiers.put("salesQuoteNo", identifier);
                        break;
                    case "ProformaInvoice":
                        identifiers.put("proformaInvoiceNo", identifier);
                        break;
                    case "JobConsumption":
                        identifiers.put("jobConsumptionNo", identifier);
                        break;
                }
            }
        }

        result.setExtractedIdentifiers(identifiers);

        if (!discrepancies.isEmpty()) {
            return VerificationStepResultDTO.failed("Document Identifier Validation",
                    "Missing required document identifiers", discrepancies);
        }

        return VerificationStepResultDTO.success("Document Identifier Validation",
                "All required identifiers extracted successfully");
    }

    /**
     * Step 3: Perform cross-document validation.
     * PREREQUISITE: All required document types and identifiers must be present
     */
    private VerificationStepResultDTO performCrossDocumentValidation(DocumentExtractionResponseDTO extractionResponse) {
        List<VerificationDiscrepancyDTO> discrepancies = new ArrayList<>();

        List<ExtractedDocumentDTO> docs = extractionResponse.getDocuments();
        if (docs.size() < 3) {
            discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                    "SYSTEM", "insufficient_documents",
                    String.format("Cross-document validation requires all 3 documents, but only %d found", docs.size()), "HIGH"));
            return VerificationStepResultDTO.failed("Cross-Document Validation",
                    "Insufficient documents for cross-document validation", discrepancies);
        }

        // Validate customer account number consistency
        validateCustomerAccountConsistency(docs, discrepancies);

        // Validate customer name consistency
        validateCustomerNameConsistency(docs, discrepancies);

        // Validate amount consistency between Sales Quote and Proforma Invoice
        validateAmountConsistency(docs, discrepancies);

        if (!discrepancies.isEmpty()) {
            return VerificationStepResultDTO.failed("Cross-Document Validation",
                    "Cross-document inconsistencies found", discrepancies);
        }

        return VerificationStepResultDTO.success("Cross-Document Validation",
                "All cross-document validations passed");
    }

    /**
     * Step 4: Perform Business Central data validation.
     * PREREQUISITE: All required identifiers must be extracted
     */
    private VerificationStepResultDTO performBusinessCentralValidation(
            String jobNo, DocumentExtractionResponseDTO extractionResponse, ComprehensiveVerificationResultDTO result) {

        List<VerificationDiscrepancyDTO> discrepancies = new ArrayList<>();

        // Check if we have all required identifiers before proceeding
        Map<String, String> identifiers = result.getExtractedIdentifiers();
        if (identifiers == null || identifiers.isEmpty()) {
            discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                    "SYSTEM", "missing_identifiers", "Cannot perform Business Central validation without document identifiers", "HIGH"));
            return VerificationStepResultDTO.failed("Business Central Validation",
                    "Missing required identifiers for Business Central validation", discrepancies);
        }

        // Validate that we have all required identifiers
        if (!identifiers.containsKey("salesQuoteNo") || identifiers.get("salesQuoteNo") == null) {
            discrepancies.add(VerificationDiscrepancyDTO.createMissingFieldDiscrepancy(
                    "SalesQuote", "Sales Quote Number", "Cannot find Sales Quote Number from Sales Quote document"));
        }

        if (!identifiers.containsKey("proformaInvoiceNo") || identifiers.get("proformaInvoiceNo") == null) {
            discrepancies.add(VerificationDiscrepancyDTO.createMissingFieldDiscrepancy(
                    "ProformaInvoice", "Tax Invoice Number", "Cannot find Tax Invoice Number from Proforma Invoice document - please check Proforma Invoice"));
        }

        if (!identifiers.containsKey("jobConsumptionNo") || identifiers.get("jobConsumptionNo") == null) {
            discrepancies.add(VerificationDiscrepancyDTO.createMissingFieldDiscrepancy(
                    "JobConsumption", "Job Shipment Number", "Cannot find Job Shipment Number from Job Consumption document"));
        }

        // If any identifiers are missing, fail the validation
        if (!discrepancies.isEmpty()) {
            return VerificationStepResultDTO.failed("Business Central Validation",
                    "Missing required identifiers for Business Central data fetching", discrepancies);
        }

        try {
            // At this point we have all required identifiers - now fetch BC data
            String salesQuoteNo = identifiers.get("salesQuoteNo");
            String proformaInvoiceNo = identifiers.get("proformaInvoiceNo");
            String jobConsumptionNo = identifiers.get("jobConsumptionNo");

            log.info("Starting Business Central data fetching for Job No: {} with identifiers: Sales Quote={}, Proforma Invoice={}, Job Consumption={}",
                    jobNo, salesQuoteNo, proformaInvoiceNo, jobConsumptionNo);

            // Fetch all verification data from Business Central
            var bcDataTuple = businessCentralService.fetchAllVerificationData(salesQuoteNo, proformaInvoiceNo, jobNo)
                    .block(); // Block to get the result synchronously

            if (bcDataTuple == null) {
                log.error("Failed to fetch Business Central data for Job No: {}", jobNo);
                discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                        "SYSTEM", "bc_fetch_error", "Failed to fetch data from Business Central", "HIGH"));
                return VerificationStepResultDTO.failed("Business Central Validation",
                        "Failed to fetch Business Central data", discrepancies);
            }

            // Extract the fetched data
            var salesQuoteDTO = bcDataTuple.getT1();
            // var salesQuoteLines = bcDataTuple.getT2(); // Not used yet
            var salesInvoiceDTO = bcDataTuple.getT3();
            // var salesInvoiceLines = bcDataTuple.getT4(); // Not used yet
            var jobLedgerEntries = bcDataTuple.getT5();

            log.info("Successfully fetched Business Central data for Job No: {}. Sales Quote found: {}, Invoice found: {}, Job Ledger Entries: {}",
                    jobNo, salesQuoteDTO != null, salesInvoiceDTO != null, jobLedgerEntries.size());

            // Validate that we found the expected records
            if (salesQuoteDTO == null) {
                discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                        "SalesQuote", "not_found_in_bc",
                        String.format("Sales Quote %s not found in Business Central", salesQuoteNo), "HIGH"));
            }

            if (salesInvoiceDTO == null) {
                discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                        "ProformaInvoice", "not_found_in_bc",
                        String.format("Proforma Invoice %s not found in Business Central", proformaInvoiceNo), "HIGH"));
            }

            if (jobLedgerEntries.isEmpty()) {
                discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                        "JobConsumption", "no_ledger_entries",
                        String.format("No job ledger entries found for Job %s in Business Central", jobNo), "MEDIUM"));
            }

            // If critical data is missing, fail the validation
            if (salesQuoteDTO == null || salesInvoiceDTO == null) {
                return VerificationStepResultDTO.failed("Business Central Validation",
                        "Critical Business Central records not found", discrepancies);
            }

            // Perform cross-validation between extracted document data and BC data
            performDocumentToBCValidation(extractionResponse, salesQuoteDTO, salesInvoiceDTO, jobLedgerEntries, discrepancies);

            if (!discrepancies.isEmpty()) {
                return VerificationStepResultDTO.failed("Business Central Validation",
                        "Business Central data validation found discrepancies", discrepancies);
            }

            return VerificationStepResultDTO.success("Business Central Validation",
                    "Business Central data validation completed successfully");

        } catch (Exception e) {
            log.error("Error during Business Central validation for Job No: {}: {}", jobNo, e.getMessage());
            discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                    "SYSTEM", "bc_validation_error", "Error accessing Business Central: " + e.getMessage(), "MEDIUM"));
            return VerificationStepResultDTO.failed("Business Central Validation",
                    "Business Central validation failed", discrepancies);
        }
    }

    /**
     * Step 5: Perform business rules validation.
     */
    private VerificationStepResultDTO performBusinessRulesValidation(DocumentExtractionResponseDTO extractionResponse) {
        List<VerificationDiscrepancyDTO> discrepancies = new ArrayList<>();

        for (ExtractedDocumentDTO doc : extractionResponse.getDocuments()) {
            if ("JobConsumption".equals(doc.getDocumentType()) && doc.getJobConsumptionData() != null) {
                // Validate signature requirements for Job Consumption
                if (doc.getJobConsumptionData().getReceivedBySignaturePresent() == null ||
                    !doc.getJobConsumptionData().getReceivedBySignaturePresent()) {
                    discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                            "JobConsumption", "received_by_signature",
                            "Job Consumption document missing required 'Received By' signature", "MEDIUM"));
                }
            }
        }

        if (!discrepancies.isEmpty()) {
            return VerificationStepResultDTO.failed("Business Rules Validation",
                    "Business rule violations found", discrepancies);
        }

        return VerificationStepResultDTO.success("Business Rules Validation",
                "All business rules validation passed");
    }

    /**
     * Validate customer account number consistency across documents.
     */
    private void validateCustomerAccountConsistency(List<ExtractedDocumentDTO> docs,
                                                   List<VerificationDiscrepancyDTO> discrepancies) {
        Set<String> accountNumbers = docs.stream()
                .map(ExtractedDocumentDTO::getCustomerAccountNumber)
                .filter(Objects::nonNull)
                .filter(s -> !s.trim().isEmpty())
                .collect(Collectors.toSet());

        if (accountNumbers.size() > 1) {
            discrepancies.add(VerificationDiscrepancyDTO.createCrossDocumentDiscrepancy(
                    "Customer Account Number",
                    "Consistent across all documents",
                    String.join(", ", accountNumbers),
                    "Customer account numbers are inconsistent across documents"));
        }
    }

    /**
     * Validate customer name consistency across documents.
     */
    private void validateCustomerNameConsistency(List<ExtractedDocumentDTO> docs,
                                                List<VerificationDiscrepancyDTO> discrepancies) {
        Set<String> customerNames = docs.stream()
                .map(ExtractedDocumentDTO::getCustomerName)
                .filter(Objects::nonNull)
                .filter(s -> !s.trim().isEmpty())
                .map(String::toLowerCase)
                .map(String::trim)
                .collect(Collectors.toSet());

        if (customerNames.size() > 1) {
            discrepancies.add(VerificationDiscrepancyDTO.createCrossDocumentDiscrepancy(
                    "Customer Name",
                    "Consistent across all documents",
                    String.join(", ", customerNames),
                    "Customer names are inconsistent across documents"));
        }
    }

    /**
     * Validate amount consistency between Sales Quote and Proforma Invoice.
     */
    private void validateAmountConsistency(List<ExtractedDocumentDTO> docs,
                                          List<VerificationDiscrepancyDTO> discrepancies) {
        String salesQuoteAmount = null;
        String proformaInvoiceAmount = null;

        for (ExtractedDocumentDTO doc : docs) {
            if ("SalesQuote".equals(doc.getDocumentType()) && doc.getSalesQuoteData() != null) {
                salesQuoteAmount = doc.getSalesQuoteData().getTotalAmountIncludingVat();
            } else if ("ProformaInvoice".equals(doc.getDocumentType()) && doc.getProformaInvoiceData() != null) {
                proformaInvoiceAmount = doc.getProformaInvoiceData().getAmount();
            }
        }

        if (salesQuoteAmount != null && proformaInvoiceAmount != null) {
            // Normalize amounts for comparison (remove commas, spaces, etc.)
            String normalizedSQAmount = normalizeAmount(salesQuoteAmount);
            String normalizedPIAmount = normalizeAmount(proformaInvoiceAmount);

            if (!normalizedSQAmount.equals(normalizedPIAmount)) {
                discrepancies.add(VerificationDiscrepancyDTO.createCrossDocumentDiscrepancy(
                        "Total Amount",
                        salesQuoteAmount,
                        proformaInvoiceAmount,
                        "Total amount mismatch between Sales Quote and Proforma Invoice"));
            }
        }
    }

    /**
     * Normalize amount string for comparison.
     */
    private String normalizeAmount(String amount) {
        if (amount == null) return "";
        return amount.replaceAll("[,\\s]", "").toLowerCase();
    }

    /**
     * Helper method to get identifier field name based on document type.
     */
    private String getIdentifierFieldName(String docType) {
        switch (docType) {
            case "SalesQuote":
                return "Sales Quote Number";
            case "ProformaInvoice":
                return "Tax Invoice Number";
            case "JobConsumption":
                return "Job Shipment Number";
            default:
                return "Document Identifier";
        }
    }

    /**
     * Helper method to get user-friendly document type name.
     */
    private String getUserFriendlyDocumentTypeName(String docType) {
        switch (docType) {
            case "SalesQuote":
                return "Sales Quote";
            case "ProformaInvoice":
                return "Proforma Invoice";
            case "JobConsumption":
                return "Job Consumption";
            default:
                return docType;
        }
    }

    /**
     * Perform cross-validation between extracted document data and Business Central data.
     */
    private void performDocumentToBCValidation(DocumentExtractionResponseDTO extractionResponse,
                                             SalesQuoteDTO salesQuoteDTO,
                                             SalesInvoiceDTO salesInvoiceDTO,
                                             List<JobLedgerEntryDTO> jobLedgerEntries,
                                             List<VerificationDiscrepancyDTO> discrepancies) {

        log.info("Starting document-to-BC validation with {} extracted documents", extractionResponse.getDocuments().size());

        // Find extracted documents by type
        ExtractedDocumentDTO salesQuoteDoc = null;
        ExtractedDocumentDTO proformaInvoiceDoc = null;
        ExtractedDocumentDTO jobConsumptionDoc = null;

        for (ExtractedDocumentDTO doc : extractionResponse.getDocuments()) {
            switch (doc.getDocumentType()) {
                case "SalesQuote":
                    salesQuoteDoc = doc;
                    break;
                case "ProformaInvoice":
                    proformaInvoiceDoc = doc;
                    break;
                case "JobConsumption":
                    jobConsumptionDoc = doc;
                    break;
            }
        }

        // Validate Sales Quote data against BC
        if (salesQuoteDoc != null && salesQuoteDTO != null && salesQuoteDoc.getSalesQuoteData() != null) {
            validateSalesQuoteAgainstBC(salesQuoteDoc, salesQuoteDTO, discrepancies);
        }

        // Validate Proforma Invoice data against BC
        if (proformaInvoiceDoc != null && salesInvoiceDTO != null && proformaInvoiceDoc.getProformaInvoiceData() != null) {
            validateProformaInvoiceAgainstBC(proformaInvoiceDoc, salesInvoiceDTO, discrepancies);
        }

        // Validate Job Consumption data against BC
        if (jobConsumptionDoc != null && !jobLedgerEntries.isEmpty() && jobConsumptionDoc.getJobConsumptionData() != null) {
            validateJobConsumptionAgainstBC(jobConsumptionDoc, jobLedgerEntries, discrepancies);
        }

        log.info("Document-to-BC validation completed with {} discrepancies found", discrepancies.size());
    }

    /**
     * Validate Sales Quote document data against Business Central Sales Quote.
     */
    private void validateSalesQuoteAgainstBC(ExtractedDocumentDTO salesQuoteDoc, SalesQuoteDTO salesQuoteDTO,
                                           List<VerificationDiscrepancyDTO> discrepancies) {
        var extractedData = salesQuoteDoc.getSalesQuoteData();

        // Validate customer information
        if (extractedData.getCustomerName() != null && salesQuoteDTO.getSellToCustomerName() != null) {
            if (!normalizeText(extractedData.getCustomerName()).equals(normalizeText(salesQuoteDTO.getSellToCustomerName()))) {
                discrepancies.add(VerificationDiscrepancyDTO.createCrossDocumentDiscrepancy(
                        "Customer Name",
                        extractedData.getCustomerName(),
                        salesQuoteDTO.getSellToCustomerName(),
                        "Customer name mismatch between Sales Quote document and Business Central"));
            }
        }

        // Validate amounts
        if (extractedData.getTotalAmountIncludingVat() != null && salesQuoteDTO.getAmountIncludingVAT() != null) {
            String extractedAmount = normalizeAmount(extractedData.getTotalAmountIncludingVat());
            String bcAmount = normalizeAmount(salesQuoteDTO.getAmountIncludingVAT().toString());

            if (!extractedAmount.equals(bcAmount)) {
                discrepancies.add(VerificationDiscrepancyDTO.createCrossDocumentDiscrepancy(
                        "Total Amount",
                        extractedData.getTotalAmountIncludingVat(),
                        salesQuoteDTO.getAmountIncludingVAT().toString(),
                        "Total amount mismatch between Sales Quote document and Business Central"));
            }
        }
    }

    /**
     * Validate Proforma Invoice document data against Business Central Sales Invoice.
     */
    private void validateProformaInvoiceAgainstBC(ExtractedDocumentDTO proformaInvoiceDoc, SalesInvoiceDTO salesInvoiceDTO,
                                                List<VerificationDiscrepancyDTO> discrepancies) {
        var extractedData = proformaInvoiceDoc.getProformaInvoiceData();

        // Validate customer information
        if (extractedData.getCustomerName() != null && salesInvoiceDTO.getSellToCustomerName() != null) {
            if (!normalizeText(extractedData.getCustomerName()).equals(normalizeText(salesInvoiceDTO.getSellToCustomerName()))) {
                discrepancies.add(VerificationDiscrepancyDTO.createCrossDocumentDiscrepancy(
                        "Customer Name",
                        extractedData.getCustomerName(),
                        salesInvoiceDTO.getSellToCustomerName(),
                        "Customer name mismatch between Proforma Invoice document and Business Central"));
            }
        }

        // Validate amounts (Note: SalesInvoiceDTO.amount represents the net amount)
        if (extractedData.getAmount() != null && salesInvoiceDTO.getAmount() != null) {
            String extractedAmount = normalizeAmount(extractedData.getAmount());
            String bcAmount = normalizeAmount(salesInvoiceDTO.getAmount().toString());

            if (!extractedAmount.equals(bcAmount)) {
                discrepancies.add(VerificationDiscrepancyDTO.createCrossDocumentDiscrepancy(
                        "Net Amount",
                        extractedData.getAmount(),
                        salesInvoiceDTO.getAmount().toString(),
                        "Net amount mismatch between Proforma Invoice document and Business Central"));
            }
        }
    }

    /**
     * Validate Job Consumption document data against Business Central Job Ledger Entries.
     */
    private void validateJobConsumptionAgainstBC(ExtractedDocumentDTO jobConsumptionDoc, List<JobLedgerEntryDTO> jobLedgerEntries,
                                               List<VerificationDiscrepancyDTO> discrepancies) {
        // var extractedData = jobConsumptionDoc.getJobConsumptionData(); // Not used yet

        // For now, just validate that we have job ledger entries
        // More specific validations can be added based on business requirements
        if (jobLedgerEntries.isEmpty()) {
            discrepancies.add(VerificationDiscrepancyDTO.createBusinessRuleDiscrepancy(
                    "JobConsumption", "no_ledger_entries",
                    "No corresponding job ledger entries found in Business Central", "MEDIUM"));
        } else {
            log.info("Job Consumption validation: Found {} job ledger entries in Business Central", jobLedgerEntries.size());
        }
    }

    /**
     * Normalize text for comparison (remove extra spaces, convert to lowercase).
     */
    private String normalizeText(String text) {
        if (text == null) return "";
        return text.trim().toLowerCase().replaceAll("\\s+", " ");
    }
}
