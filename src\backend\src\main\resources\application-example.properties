# Set the server port to 8081 to avoid conflict with the default 8080
server.port=8081

# LLM Microservice Configuration
# For local development (when running outside Docker)
llm.python.service.baseurl=http://localhost:8001
# For Docker environment (uncomment when running in Docker)
# llm.python.service.baseurl=http://gemini-python-service:8000

# Dynamics 365 Business Central Configuration
dynamics.bc.odata.base-url=https://your-bc-instance.com:7048/BC160/ODataV4/Company('YOUR_COMPANY')
dynamics.bc.odata.username=${DYNAMICS_BC_USERNAME}
# Use environment variables for sensitive information
dynamics.bc.odata.key=${DYNAMICS_BC_API_KEY}
# Field name in Business Central Job entity to update with AI status
dynamics.bc.odata.status-field-name=AI_Verification_Status

# RabbitMQ Configuration (Defaults for local setup)
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
# Provide username/password if your RabbitMQ setup requires authentication
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}

# Tesseract OCR Configuration
# tesseract.path=... # Optional: Only needed if Tesseract executable is not in system PATH
tesseract.datapath=/path/to/tessdata

# MySQL Database Configuration
spring.datasource.url=******************************************************************************************************************************
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# Explicitly set the dialect for MySQL 8+
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
# Automatically update the schema based on JPA entities
spring.jpa.hibernate.ddl-auto=update

# Logging Configuration
logging.level.com.erp.aierpbackend.listener=DEBUG
# Added for detailed verification logs
logging.level.com.erp.aierpbackend.service.JobDocumentVerificationService=DEBUG

