# Set the server port to 8081 to avoid conflict with the default 8080
server.port=8081

# LLM Microservice Configuration
# The base URL for the Gemini Python microservice
# This can be overridden with environment variable LLM_PYTHON_SERVICE_BASEURL
# Default for local development: http://localhost:8000
# Default for Docker environment: http://gemini-python-service:8000
llm.python.service.baseurl=${LLM_PYTHON_SERVICE_BASEURL:http://localhost:8000}

# Google Gemini Configuration Removed (Service deleted)
# gemini.api.key=...
# gemini.api.endpoint=...

# Resilience4j Configuration Removed for Gemini (Service deleted)
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.registerHealthIndicator=true
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.slidingWindowType=COUNT_BASED
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.slidingWindowSize=10
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.minimumNumberOfCalls=5
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.permittedNumberOfCallsInHalfOpenState=3
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.automaticTransitionFromOpenToHalfOpenEnabled=true
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.waitDurationInOpenState=5s
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.failureRateThreshold=50
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.automaticTransitionFromOpenToHalfOpenEnabled=true
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.waitDurationInOpenState=5s
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.failureRateThreshold=50
# resilience4j.circuitbreaker.instances.geminiCircuitBreaker.eventConsumerBufferSize=10
#
# resilience4j.retry.instances.geminiRetry.maxAttempts=3
# resilience4j.retry.instances.geminiRetry.waitDuration=500ms
# resilience4j.retry.instances.geminiRetry.retryExceptions=org.springframework.web.reactive.function.client.WebClientResponseException,java.util.concurrent.TimeoutException,java.io.IOException
# resilience4j.retry.instances.geminiRetry.ignoreExceptions=

# Dynamics 365 Business Central Configuration
dynamics.bc.odata.base-url=https://bctest.dayliff.com:7048/BC160/ODataV4/Company('KENYA')
dynamics.bc.odata.username=${DYNAMICS_BC_USERNAME:webservice}
# Using environment variables for sensitive information
dynamics.bc.odata.key=${DYNAMICS_BC_API_KEY}
# Field name in Business Central Job entity to update with AI status
dynamics.bc.odata.status-field-name=AI_Verification_Status

# SharePoint Configuration - Microsoft Graph API
# Client ID and Secret for the Azure AD application
sharepoint.client.id=${SHAREPOINT_CLIENT_ID:56ee932a-3ceb-40db-9727-cfee3d66501f}
sharepoint.client.secret=${SHAREPOINT_CLIENT_SECRET:****************************************}
# Tenant ID for the Azure AD tenant
sharepoint.tenant.id=${SHAREPOINT_TENANT_ID:e9e12402-b3ab-458f-a106-d7b5007b75fc}
# Enable more detailed logging for SharePoint and WebClient
logging.level.com.erp.aierpbackend.service.SharePointService=TRACE
logging.level.org.springframework.web.reactive.function.client.ExchangeFunctions=TRACE
logging.level.reactor.netty.http.client=TRACE
logging.level.org.springframework.web.reactive.function.client=TRACE
# Increase timeouts for WebClient
spring.webflux.client.max-in-memory-size=16MB
spring.codec.max-in-memory-size=16MB

# Netty DNS Resolver Configuration
# Enable detailed logging for DNS resolution
logging.level.io.netty.resolver.dns=DEBUG
# Configure DNS resolver timeouts
reactor.netty.dns.timeout=10000
# Configure connection timeouts
reactor.netty.connection.timeout=10000

# RabbitMQ Configuration (Defaults for local setup)
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
# Provide username/password if your RabbitMQ setup requires authentication
# spring.rabbitmq.username=guest
# spring.rabbitmq.password=guest

# Tesseract OCR Configuration
# tesseract.path=... # Optional: Only needed if Tesseract executable is not in system PATH
tesseract.datapath=C:/Program Files/Tesseract-OCR/tessdata

# MySQL Database Configuration
spring.datasource.url=******************************************************************************************************************************
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# Explicitly set the dialect for MySQL 8+
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
# Automatically update the schema based on JPA entities
spring.jpa.hibernate.ddl-auto=update

# Logging Configuration
logging.level.com.erp.aierpbackend.listener=DEBUG
# Added for detailed verification logs
logging.level.com.erp.aierpbackend.service.JobDocumentVerificationService=DEBUG


# JWT Configuration
app.jwt.secret=aierpSecretKey123456789012345678901234567890123456789012345678901234567890
app.jwt.expiration-ms=86400000
