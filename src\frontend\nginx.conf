# Specify PID file location that nginx user can write to
pid /var/run/nginx.pid;

server {
  listen 8080;
  server_name localhost;

  root /usr/share/nginx/html;
  index index.html index.htm;

  # API requests are handled directly by the frontend to the backend URL
  # No proxy needed since we're using direct backend URL in the frontend

  location / {
    try_files $uri $uri/ /index.html;
  }
  location/login{
    proxy_pass http://localhost:5173;
  }

  # Health check endpoint for Cloud Run
  location /health {
    access_log off;
    return 200 "healthy\n";
    add_header Content-Type text/plain;
  }

  # Optional: Add headers to prevent caching issues
  # location ~* \.(?:ico|css|js|gif|jpe?g|png)$ {
  #   expires 1y;
  #   add_header Cache-Control "public";
  # }
}
