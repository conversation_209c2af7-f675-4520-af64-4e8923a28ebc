"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "../../components/ui/tabs"
import { useState, useEffect } from "react"
import { getDailyVerificationStats } from "../../lib/api"
import { Skeleton } from "../ui/skeleton"

export function VerificationTrends() {
  const [dailyData, setDailyData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        const data = await getDailyVerificationStats()
        setDailyData(data)
      } catch (err) {
        setError("Failed to load verification trends")
        console.error(err)
      } finally {
        setIsLoading(false)
      }
    }
    fetchData()
  }, [])

  const weeklyData = [
    { name: "Week 1", verified: 350, flagged: 65, pending: 85 },
    { name: "Week 2", verified: 390, flagged: 72, pending: 78 },
    { name: "Week 3", verified: 420, flagged: 58, pending: 62 },
    { name: "Week 4", verified: 380, flagged: 70, pending: 90 },
  ]

  const monthlyData = [
    { name: "Jan", verified: 1200, flagged: 250, pending: 350 },
    { name: "Feb", verified: 1350, flagged: 220, pending: 330 },
    { name: "Mar", verified: 1450, flagged: 280, pending: 270 },
    { name: "Apr", verified: 1540, flagged: 265, pending: 315 },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Verification Trends</CardTitle>
        <CardDescription>Track verification results over time</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Skeleton className="h-[300px] w-full" />
        ) : error ? (
          <div className="text-red-500 text-center py-8">{error}</div>
        ) : (
          <Tabs defaultValue="daily">
            <TabsList className="mb-4">
              <TabsTrigger value="daily">Daily</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
            </TabsList>
            <TabsContent value="daily">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={dailyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="verified" name="Verified" stroke="#22c55e" strokeWidth={2} />
                    <Line type="monotone" dataKey="flagged" name="Flagged" stroke="#ef4444" strokeWidth={2} />
                    <Line type="monotone" dataKey="pendingOrError" name="Pending/Error" stroke="#f59e0b" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>
          <TabsContent value="weekly">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={weeklyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="verified" name="Verified" stroke="#22c55e" strokeWidth={2} />
                  <Line type="monotone" dataKey="flagged" name="Flagged" stroke="#ef4444" strokeWidth={2} />
                  <Line type="monotone" dataKey="pending" name="Pending" stroke="#f59e0b" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          <TabsContent value="monthly">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="verified" name="Verified" stroke="#22c55e" strokeWidth={2} />
                  <Line type="monotone" dataKey="flagged" name="Flagged" stroke="#ef4444" strokeWidth={2} />
                  <Line type="monotone" dataKey="pending" name="Pending" stroke="#f59e0b" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}

