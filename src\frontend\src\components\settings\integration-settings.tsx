"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card"
import { Button } from "../../components/ui/button"
import { Input } from "../../components/ui/input"
import { Label } from "../../components/ui/label"
import { Switch } from "../../components/ui/switch"
import { useState } from "react"

export function IntegrationSettings() {
  const [settings, setSettings] = useState({
    businessCentralUrl: "https://bctest.dayliff.com:7048/BC160",
    apiKey: "••••••••••••••••",
    autoSync: true,
    syncInterval: "30",
    geminiApiKey: "••••••••••••••••",
    confidenceThreshold: "0.8",
  })

  const handleChange = (key: keyof typeof settings, value: string) => {
    setSettings({
      ...settings,
      [key]: value,
    })
  }

  const handleToggle = (key: keyof typeof settings) => {
    setSettings({
      ...settings,
      [key]: !settings[key],
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>System Integration Settings</CardTitle>
        <CardDescription>Configure Business Central and AI service connections</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="businessCentralUrl">Business Central URL</Label>
            <Input
              id="businessCentralUrl"
              value={settings.businessCentralUrl}
              onChange={(e) => handleChange("businessCentralUrl", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="apiKey">Business Central API Key</Label>
            <Input
              id="apiKey"
              type="password"
              value={settings.apiKey}
              onChange={(e) => handleChange("apiKey", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="geminiApiKey">Gemini AI API Key</Label>
            <Input
              id="geminiApiKey"
              type="password"
              value={settings.geminiApiKey}
              onChange={(e) => handleChange("geminiApiKey", e.target.value)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="autoSync">Automatic Job Processing</Label>
              <div className="text-sm text-muted-foreground">Automatically process new jobs from Business Central</div>
            </div>
            <Switch id="autoSync" checked={settings.autoSync} onCheckedChange={() => handleToggle("autoSync")} />
          </div>

          <div className="space-y-2">
            <Label htmlFor="syncInterval">Processing Interval (minutes)</Label>
            <Input
              id="syncInterval"
              type="number"
              value={settings.syncInterval}
              onChange={(e) => handleChange("syncInterval", e.target.value)}
              disabled={!settings.autoSync}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confidenceThreshold">AI Confidence Threshold</Label>
            <Input
              id="confidenceThreshold"
              type="number"
              step="0.1"
              min="0"
              max="1"
              value={settings.confidenceThreshold}
              onChange={(e) => handleChange("confidenceThreshold", e.target.value)}
            />
            <div className="text-sm text-muted-foreground">Minimum confidence level for automatic verification (0.0 - 1.0)</div>
          </div>

          <div className="flex gap-2">
            <Button className="flex-1">Save Integration Settings</Button>
            <Button variant="outline">Test Connection</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

