"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card"
import { Button } from "../../components/ui/button"
import { Input } from "../../components/ui/input"
import { Label } from "../../components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "../../components/ui/avatar"
import { useState, useEffect } from "react"
import { getCurrentUser } from "../../services/authService"

export function UserSettings() {
  const [user, setUser] = useState({
    name: "",
    email: "",
    role: "",
    avatar: "/placeholder.svg?height=100&width=100",
  })

  // Load user data on component mount
  useEffect(() => {
    const currentUser = getCurrentUser();
    if (currentUser) {
      setUser({
        name: currentUser.username || "",
        email: currentUser.email || "",
        role: currentUser.roles[0]?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || "User",
        avatar: "/placeholder.svg?height=100&width=100",
      });
    }
  }, []);

  // Generate initials from username
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2) || 'U';
  };

  const handleChange = (key: keyof typeof user, value: string) => {
    setUser({
      ...user,
      [key]: value,
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Profile</CardTitle>
        <CardDescription>Manage your account information</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex flex-col items-center gap-4 sm:flex-row">
            <Avatar className="h-20 w-20">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-2 text-center sm:text-left">
              <div className="space-y-0.5">
                <h3 className="text-lg font-semibold">{user.name}</h3>
                <p className="text-sm text-muted-foreground">{user.role}</p>
              </div>
              <Button variant="outline" size="sm">
                Change Avatar
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input id="name" value={user.name} onChange={(e) => handleChange("name", e.target.value)} />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input id="email" type="email" value={user.email} onChange={(e) => handleChange("email", e.target.value)} />
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Input id="role" value={user.role} onChange={(e) => handleChange("role", e.target.value)} disabled />
            <div className="text-sm text-muted-foreground">Contact your administrator to change your role</div>
          </div>

          <div className="flex gap-2">
            <Button className="flex-1">Save Profile</Button>
            <Button variant="outline">Change Password</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

