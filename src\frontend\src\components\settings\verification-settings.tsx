"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "../../components/ui/card"
import { Button } from "../../components/ui/button"
import { Input } from "../../components/ui/input"
import { Label } from "../../components/ui/label"
import { Switch } from "../../components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select"
import { useState } from "react"

export function VerificationSettings() {
  const [settings, setSettings] = useState({
    autoVerification: true,
    requireManualReview: false,
    maxRetries: "3",
    timeoutMinutes: "10",
    documentTypes: "all",
    saveFailedDocuments: true,
  })

  const handleChange = (key: keyof typeof settings, value: string) => {
    setSettings({
      ...settings,
      [key]: value,
    })
  }

  const handleToggle = (key: keyof typeof settings) => {
    setSettings({
      ...settings,
      [key]: !settings[key],
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Document Verification Settings</CardTitle>
        <CardDescription>Configure how documents are processed and verified</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="autoVerification">Automatic Verification</Label>
              <div className="text-sm text-muted-foreground">Enable automatic document verification</div>
            </div>
            <Switch
              id="autoVerification"
              checked={settings.autoVerification}
              onCheckedChange={() => handleToggle("autoVerification")}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="requireManualReview">Require Manual Review</Label>
              <div className="text-sm text-muted-foreground">Always require human review before finalizing</div>
            </div>
            <Switch
              id="requireManualReview"
              checked={settings.requireManualReview}
              onCheckedChange={() => handleToggle("requireManualReview")}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxRetries">Maximum Retry Attempts</Label>
            <Input
              id="maxRetries"
              type="number"
              min="1"
              max="10"
              value={settings.maxRetries}
              onChange={(e) => handleChange("maxRetries", e.target.value)}
            />
            <div className="text-sm text-muted-foreground">Number of times to retry failed verifications</div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="timeoutMinutes">Processing Timeout (minutes)</Label>
            <Input
              id="timeoutMinutes"
              type="number"
              min="1"
              max="60"
              value={settings.timeoutMinutes}
              onChange={(e) => handleChange("timeoutMinutes", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="documentTypes">Document Types to Process</Label>
            <Select value={settings.documentTypes} onValueChange={(value) => handleChange("documentTypes", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select document types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Document Types</SelectItem>
                <SelectItem value="invoices">Proforma Invoices Only</SelectItem>
                <SelectItem value="quotes">Sales Quotes Only</SelectItem>
                <SelectItem value="consumption">Job Consumption Only</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="saveFailedDocuments">Save Failed Documents</Label>
              <div className="text-sm text-muted-foreground">Keep copies of documents that fail verification</div>
            </div>
            <Switch
              id="saveFailedDocuments"
              checked={settings.saveFailedDocuments}
              onCheckedChange={() => handleToggle("saveFailedDocuments")}
            />
          </div>

          <Button className="w-full">Save Verification Settings</Button>
        </div>
      </CardContent>
    </Card>
  )
}
