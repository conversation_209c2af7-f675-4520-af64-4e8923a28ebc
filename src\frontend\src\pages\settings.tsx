import { SettingsHeader } from "../components/settings/settings-header.tsx"
import { NotificationSettings } from "../components/settings/notification-settings"
import { IntegrationSettings } from "../components/settings/integration-settings"
import { ThemeSettings } from "../components/settings/theme-settings"
import { UserSettings } from "../components/settings/user-settings"
import { VerificationSettings } from "../components/settings/verification-settings"

export default function Settings() {
  return (
    <main className="flex min-h-screen flex-col p-6 max-w-7xl mx-auto">
      <SettingsHeader />

      <div className="grid gap-6 lg:grid-cols-2 mt-6">
        <UserSettings />
        <ThemeSettings />
      </div>

      <div className="grid gap-6 lg:grid-cols-2 mt-6">
        <VerificationSettings />
        <NotificationSettings />
      </div>

      <div className="mt-6">
        <IntegrationSettings />
      </div>
    </main>
  )
}

