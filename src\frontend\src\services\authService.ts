import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081/api';

// Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface SignupRequest {
  username: string;
  email: string;
  password: string;
  roles?: string[];
}

export interface JwtResponse {
  token: string;
  type: string;
  id: number;
  username: string;
  email: string;
  roles: string[];
}

// Login function
export const login = async (loginRequest: LoginRequest): Promise<JwtResponse> => {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/signin`, loginRequest);

    // Store JWT token and user data in localStorage
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('userRole', response.data.roles[0]); // Store the first role
      localStorage.setItem('userData', JSON.stringify({
        id: response.data.id,
        username: response.data.username,
        email: response.data.email,
        roles: response.data.roles
      }));
    }

    return response.data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

// Register function
export const register = async (signupRequest: SignupRequest): Promise<any> => {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/signup`, signupRequest);
    return response.data;
  } catch (error) {
    console.error('Registration error:', error);
    throw error;
  }
};

// Logout function
export const logout = (): void => {
  localStorage.removeItem('token');
  localStorage.removeItem('userRole');
  localStorage.removeItem('userData');
};

// Get current user from token
export const getCurrentUser = (): JwtResponse | null => {
  const token = localStorage.getItem('token');
  const userData = localStorage.getItem('userData');

  if (token && userData) {
    try {
      const parsedUserData = JSON.parse(userData);
      return {
        token,
        type: 'Bearer',
        id: parsedUserData.id,
        username: parsedUserData.username,
        email: parsedUserData.email,
        roles: parsedUserData.roles
      };
    } catch (error) {
      console.error('Error parsing user data:', error);
      // Fallback to basic object if parsing fails
      return {
        token,
        type: 'Bearer',
        id: 0,
        username: '',
        email: '',
        roles: [localStorage.getItem('userRole') || '']
      };
    }
  }
  return null;
};

// Check if user is authenticated and token is not expired
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('token');
  if (!token) {
    return false;
  }

  try {
    // Decode JWT token to check expiration (basic check without verification)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Date.now() / 1000;

    // Check if token is expired
    if (payload.exp && payload.exp < currentTime) {
      // Token is expired, remove it
      logout();
      return false;
    }

    return true;
  } catch (error) {
    // If token is malformed, consider it invalid
    logout();
    return false;
  }
};

// Setup axios interceptor for authentication
export const setupAxiosInterceptors = (): void => {
  axios.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers['Authorization'] = 'Bearer ' + token;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Add a response interceptor to handle token expiration
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response && error.response.status === 401) {
        // Token expired or invalid, logout the user
        logout();
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );
};
