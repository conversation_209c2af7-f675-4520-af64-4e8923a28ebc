import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
  ],
  resolve: {
    alias: {
      "@": "/src", // Relative to project root
    },
  },
  server: {
    host: true, // Allow external connections
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      '.ngrok.io',
      '.ngrok-free.app',
      '.ngrok.app'
    ],
    proxy: {
      // Proxy API requests starting with /api to the backend server
      '/api': {
        target: 'http://localhost:8081', // Your backend server address (running on port 8081)
        changeOrigin: true, // Needed for virtual hosted sites
        secure: false,      // Optional: Set to false if backend is not using HTTPS
        // Optional: Rewrite path if needed (e.g., remove /api prefix)
        // rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
});