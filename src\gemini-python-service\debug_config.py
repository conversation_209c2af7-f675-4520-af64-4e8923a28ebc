#!/usr/bin/env python3
"""
Debug script to test configuration loading
"""

import os
from pathlib import Path

# Test 1: Check current working directory
print("Current working directory:", os.getcwd())

# Test 2: Check if .env file exists
env_file_path = Path(__file__).parent / '.env'
print(f".env file path: {env_file_path}")
print(f".env file exists: {env_file_path.exists()}")

# Test 3: Check the path that config.py is using
config_env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), ".env")
print(f"Config.py .env path: {config_env_path}")
print(f"Config.py .env exists: {os.path.exists(config_env_path)}")

# Test 4: Try to load environment variables manually
if env_file_path.exists():
    print("\n--- Environment variables from .env file ---")
    with open(env_file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                print(f"{key}: {value[:50]}...")

# Test 5: Try to import the config
print("\n--- Trying to import config ---")
try:
    from app.config import Settings
    print("Settings class imported successfully")
    
    # Try to create settings instance
    settings = Settings()
    print("Settings instance created successfully!")
    print(f"GCP Project ID: {settings.gcp_project_id}")
    print(f"GCP Location: {settings.gcp_location}")
    print(f"Gemini Model Name: {settings.gemini_model_name}")
    
except Exception as e:
    print(f"Error importing/creating settings: {e}")
    import traceback
    traceback.print_exc()
